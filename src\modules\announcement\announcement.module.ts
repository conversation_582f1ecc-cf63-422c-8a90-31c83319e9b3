import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Announcement, AnnouncementSchema } from './announcement.schema';
import { AnnouncementService } from './announcement.service';
import { AnnouncementController } from './announcement.controller';
import { AreaModule } from '../area/area.module';
import { DifyModule } from '../dify/dify.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Announcement.name, schema: AnnouncementSchema },
    ]),
    AreaModule,
    DifyModule,
  ],
  providers: [AnnouncementService],
  controllers: [AnnouncementController],
  exports: [AnnouncementService],
})
export class AnnouncementModule {}
