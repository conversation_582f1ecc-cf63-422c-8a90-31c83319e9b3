import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  HttpException,
  HttpStatus,
  Request,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthGuard } from 'src/guards/auth.guard';
import { UserService } from '../user/user.service';

@Controller('/api/auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private userService: UserService,
  ) {}

  @Post('login')
  async login(@Body() body: { username: string; password: string }) {
    const { username, password } = body;
    if (!username.endsWith('@hxdi.com')) {
      throw new HttpException(
        '请使用华信工作邮箱登录！',
        HttpStatus.BAD_REQUEST,
      );
    }
    const user = await this.authService.validateUser(username, password);
    if (!user) {
      return this.authService.register(body.username, body.password);
    }
    return this.authService.login(user);
  }

  @Post('register')
  async register(@Body() body: { username: string; password: string }) {
    return this.authService.register(body.username, body.password);
  }

  @Post('resetPwd')
  async resetPwd(@Body() body: { username: string }) {
    return this.userService.resetPassword(body.username);
  }

  @Post('changePwd')
  @UseGuards(AuthGuard)
  async changePwd(
    @Request() req,
    @Body() body: { oldPassword: string; newPassword: string },
  ) {
    return this.userService.changePassword(
      req.user.userId,
      body.oldPassword,
      body.newPassword,
    );
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  getProfile(@Req() req: Request) {
    return req;
  }
}
