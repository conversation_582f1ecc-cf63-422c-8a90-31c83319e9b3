// import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
// import { Cron } from '@nestjs/schedule';
// import { AnnouncementService } from '../announcement/announcement.service';
// import { SubscribeService } from './subscribe.service';
// import { ConfigService } from '@nestjs/config';
// import * as nodemailer from 'nodemailer';

// @Injectable()
// export class SubscribeCronService implements OnModuleInit {
//   private readonly logger = new Logger('【邮件推送】：');
//   constructor(
//     private readonly announcementService: AnnouncementService,
//     private readonly subscribeService: SubscribeService,
//     private readonly configService: ConfigService,
//   ) {}

//   onModuleInit() {
//     this.sendDailyAnnouncements();
//   }

//   @Cron('3 3 17 * *') // 每天15点执行
//   async sendDailyAnnouncements() {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0); // 设置时间为当天的开始时间

//     const allSubscribes = await this.subscribeService.findAll();

//     for (const subscribe of allSubscribes) {
//       const announcements =
//         await this.announcementService.findAnnouncementsByCondition(
//           subscribe.areaCode,
//           subscribe.keywords,
//           today,
//         );

//       const transporter = nodemailer.createTransport({
//         host: this.configService.get<string>('EMAIL_HOST'),
//         auth: {
//           user: this.configService.get<string>('EMAIL_SENDER_USER'),
//           pass: this.configService.get<string>('EMAIL_SENDER_PASSWORD'),
//         },
//       });

//       const text = announcements
//         .map(
//           (announcement) => `##${announcement.title}\n${announcement.content}`,
//         )
//         .join('\n--------分割线--------\n');
//       const subject = `【${today.toLocaleDateString()}】商机推送`;
//       const mailOptions = {
//         from: this.configService.get<string>('EMAIL_USER'),
//         to: subscribe.email,
//         subject: subject,
//         text: text,
//       };

//       await transporter.sendMail(mailOptions);
//       this.logger.log(
//         `邮件发送成功, email: ${subscribe.email}, subject: ${subject}`,
//       );
//     }
//     // const announcements = await this.announcementService.findAnnouncementsByPublishDate(today);

//     // if (announcements.length === 0) {
//     //   console.log('No announcements found for today.');
//     //   return;
//     // }

//     // for (const announcement of announcements) {
//     //   const subscribes = await this.subscribeService.findAll();

//     //   if (subscribes.length === 0) {
//     //     console.log(`No subscribes found for announcement: ${announcement.title}`);
//     //     continue;
//     //   }

//     //   const transporter = nodemailer.createTransport({
//     //     service: 'gmail',
//     //     auth: {
//     //       user: this.configService.get<string>('EMAIL_USER'),
//     //       pass: this.configService.get<string>('EMAIL_PASS'),
//     //     },
//     //   });

//     //   for (const subscribe of subscribes) {
//     //     const mailOptions = {
//     //       from: this.configService.get<string>('EMAIL_USER'),
//     //       to: subscribe.email,
//     //       subject: announcement.title,
//     //       text: announcement.content,
//     //     };

//     //     await transporter.sendMail(mailOptions);
//     //     console.log(`Email sent to ${subscribe.email} for announcement: ${announcement.title}`);
//     //   }
//     // }
//   }
// }
