import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { AnnouncementService } from '../announcement/announcement.service';
import { SubscribeService } from '../subscribe/subscribe.service';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../user/user.service';
import * as nodemailer from 'nodemailer';
import { AreaService } from '../area/area.service';

@Injectable()
export class ScheduleService implements OnModuleInit {
  private readonly logger = new Logger('【邮件推送】：');
  private transporter;
  constructor(
    private readonly announcementService: AnnouncementService,
    private readonly subscribeService: SubscribeService,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly areaService: AreaService,
  ) {}

  onModuleInit() {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get<string>('EMAIL_HOST'),
      secureConnection: false,
      port: 25,
      // auth: {
      //   user: this.configService.get<string>('EMAIL_SENDER_USER'),
      //   pass: this.configService.get<string>('EMAIL_SENDER_PASSWORD'),
      // },
      tls: {
        ciphers: 'DEFAULT@SECLEVEL=0',
        rejectUnauthorized: false, // 如果使用自签名证书，可以设置为 false
      },
      // debug: true, // 启用调试模式
      logger: true, // 启用日志记录
    });
    // this.sendDailyAnnouncements();
  }

  @Cron('00 09 * * *', { timeZone: 'Asia/Shanghai' }) // 每天9点执行
  // 定义一个异步函数，用于发送每日公告邮件
  async sendDailyAnnouncements() {
    // 记录日志，表示开始推送邮件
    this.logger.log('开始推送邮件');
    // 获取当前日期
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const utcYesterday = new Date(yesterday.setUTCHours(0, 0, 0, 0));

    const fromDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay() - 6,
      0,
      0,
      0,
    );
    const toDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - today.getDay(),
      23,
      59,
      59,
    );
    const allSubscribes = await this.subscribeService.findAll();
    // 根据用户分组
    let subscribesUserList = [];
    allSubscribes.forEach(async (subscribe) => {
      const { userId, type } = subscribe;
      const data = subscribesUserList.find((item) => item.userId === userId);
      if (!data) {
        subscribesUserList.push({
          userId,
          bid: subscribe,
          win: subscribe,
        });
      } else if (type === '1') {
        data.bid = subscribe;
      } else if (type === '2') {
        data.win = subscribe;
      }
    });
    subscribesUserList = subscribesUserList.filter(
      (val) => val.bid?.keywords?.length > 0 || val.win?.keywords?.length > 0,
    );
    const getDateString = (date) => {
      const today = new Date(date);
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      return `${month}-${day}`;
    };
    // 斑马线表格+关键词高亮
    const buildStripeTable = (data, keyword) => {
      if (!data.length) return '<div>无</div>';
      const columns = [
        '序号',
        '公告名称',
        '地区',
        '建设内容',
        '预算（万）',
        '公告链接',
      ];
      const keywordReg = keyword
        ? new RegExp(
            `(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
            'gi',
          )
        : null;

      let html = `
    <table border="1" cellpadding="5" cellspacing="0" style="border-collapse:collapse;width:100%;">
      <thead>
        <tr style="background:#5B9BD5;">
          ${columns
            .map(
              (col) =>
                `<th style="border:1px solid #9CC2E5;color: white;font-weight:bold;text-align: center;">${col}</th>`,
            )
            .join('')}
        </tr>
      </thead>
      <tbody>
  `;

      data.forEach((row, idx) => {
        html += `<tr style="background:${
          idx % 2 === 0 ? '#fff' : '#DEEAF6'
        };">`;
        html += `<td style="width: 75px;text-align: center;border:1px solid #9CC2E5;">${
          idx + 1
        }</td>`;
        let cell = row.title || '';
        if (keywordReg) {
          cell = cell.replace(
            keywordReg,
            '<span style="color:#ed7d31;font-weight:bold;">$1</span>',
          );
        }
        html += `<td style="border:1px solid #9CC2E5;">${cell}</td>`;
        html += `<td style="width: 110px;text-align: center;border:1px solid #9CC2E5;">${row.area}</td>`;
        html += `<td style="border:1px solid #9CC2E5;">
        ${row?.constructionContent?.join('，') || ''}</td>`;
        html += `<td style="width: 120px;text-align: center;border:1px solid #9CC2E5;">
        ${row?.budgetAmount || ''}${row?.budgetUnit || ''}</td>`;
        html += `<td style="width: 145px;text-align: center;border:1px solid #9CC2E5;"><a href="${row.url}" target="_blank" style="color:#0070c0;">查看详情</a></td>`;
        html += '</tr>';
      });

      html += '</tbody></table>';
      return html;
    };

    const buildKeywords = async (areaCode, keywords, type) => {
      let html = '';
      if (keywords.length) {
        for (const [index, keyword] of keywords.entries()) {
          try {
            const list =
              await this.announcementService.findAnnouncementsByCondition(
                areaCode,
                [keyword as string],
                utcYesterday,
                type,
              );
            html += `<div style="margin:20px 0;font-weight:bold;">订阅词${
              index + 1
            }：${keyword}：（${list.length}份）</div>`;
            html += buildStripeTable(list, keyword);
          } catch (error) {
            this.logger.error(`处理订阅词 ${keyword} 时出错: ${error}`);
          }
        }
      } else {
        html += '<div>无</div>';
      }
      return html;
    };

    // 上周商机资讯回顾表格
    const buildLastWeekTable = async (data, keyword) => {
      if (!data.length) return '<div>无</div>';
      // 先按日期分组
      const groupByDate = {};
      data.forEach((item) => {
        const date = item.publishDate ? getDateString(item.publishDate) : '';
        if (!groupByDate[date]) groupByDate[date] = [];
        groupByDate[date].push(item);
      });

      const keywordReg = keyword
        ? new RegExp(
            `(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
            'gi',
          )
        : null;
      const columns = [
        '发布日期',
        '公告名称',
        '地区',
        '建设内容',
        '预算（万）',
        '公告链接',
      ];

      let html = `
    <table border="1" cellpadding="5" cellspacing="0" style="border-collapse:collapse;width:100%;">
      <thead>
        <tr style="background:#5B9BD5;">
          ${columns
            .map(
              (col) =>
                `<th style="border:1px solid #9CC2E5;color: white;font-weight:bold;text-align: center;">${col}</th>`,
            )
            .join('')}
        </tr>
      </thead>
      <tbody>
  `;

      let rowIdx = 0;
      for (const date of Object.keys(groupByDate).sort()) {
        const group = groupByDate[date];
        group.forEach((row, idx) => {
          html += `<tr style="background:${
            rowIdx % 2 === 0 ? '#fff' : '#DEEAF6'
          };">`;
          if (idx === 0) {
            html += `<td style="width: 75px;text-align: center;border:1px solid #9CC2E5;" rowspan="${group.length}">${date}</td>`;
          }
          let cell = row.title || '';
          if (keywordReg) {
            cell = cell.replace(
              keywordReg,
              '<span style="color:#ed7d31;font-weight:bold;">$1</span>',
            );
          }
          html += `<td style="border:1px solid #9CC2E5;">${cell}</td>`;
          html += `<td style="width: 110px;text-align: center;border:1px solid #9CC2E5;">${row.area}</td>`;
          html += `<td style="border:1px solid #9CC2E5;">
          ${row?.constructionContent?.join('，') || ''}</td>`;
          html += `<td style="width: 120px;text-align: center;border:1px solid #9CC2E5;">
          ${row?.budgetAmount || ''}${row?.budgetUnit || ''}</td>`;
          html += `<td style="width: 145px;text-align: center;border:1px solid #9CC2E5;"><a href="${row.url}" target="_blank" style="color:#0070c0;">查看详情</a></td>`;
          html += '</tr>';
          rowIdx++;
        });
      }

      html += '</tbody></table>';
      return html;
    };

    try {
      // 遍历所有订阅记录
      for (const subscribe of subscribesUserList) {
        // 昨日订阅-招标公告
        const bidList =
          subscribe.bid?.keywords?.length > 0
            ? await this.announcementService.findAnnouncementsByCondition(
                subscribe.bid.areaCode,
                subscribe.bid.keywords,
                utcYesterday,
                '1',
              )
            : [];
        // 昨日订阅-中标公告
        const winList =
          subscribe.win?.keywords?.length > 0
            ? await this.announcementService.findAnnouncementsByCondition(
                subscribe.win.areaCode,
                subscribe.win.keywords,
                utcYesterday,
                '2',
              )
            : [];

        const bidKeywords = Array.from(new Set(subscribe.bid.keywords)) || [];
        const winKeywords = Array.from(new Set(subscribe.win.keywords)) || [];
        const bidArea = await this.areaService.getAreaNameByCode(
          subscribe.bid.areaCode,
        );
        const winArea = await this.areaService.getAreaNameByCode(
          subscribe.win.areaCode,
        );
        const toUser = await this.userService.findUserById(subscribe.userId);
        if (toUser) {
          try {
            // 邮件内容拼接
            let html = `
              <p>尊敬的 ${toUser.username}，您好！</p>
              <p>根据订阅的关键词，我们为您筛选出以下最新标书信息，请及时关注：</p>
              <h2>昨日商机总览</h2>
              <ul>
                <li>招标公告订阅关键词：${bidKeywords.join('、') || '无'}</li>
                <li>招标公告订阅地区：${bidArea || '全国'}</li>
                新增招标公告：${bidList.length}份
                <li>中标公告订阅关键词：${winKeywords.join('、') || '无'}</li>
                <li>中标公告订阅地区：${winArea || '全国'}</li>
                新增中标公告：${winList.length}份
              </ul>
              <h2>招标公告列表</h2>
              ${await buildKeywords(subscribe.bid.areaCode, bidKeywords, '1')}
              <hr>
              <h2>中标公告列表</h2>
              ${await buildKeywords(subscribe.win.areaCode, winKeywords, '2')}
              <hr>
              <h2>上周商机资讯回顾</h2>
            `;
            // 每个招标订阅词一个上周表格
            if (bidKeywords.length) {
              for (const [index, keyword] of bidKeywords.entries()) {
                try {
                  const list =
                    await this.announcementService.findAnnouncementsByConditionDateRangeNoPage(
                      subscribe.bid.areaCode,
                      [keyword as string],
                      fromDate,
                      toDate,
                      '1',
                    );
                  html += `<div style="margin:20px 0;font-weight:bold;">订阅词${
                    index + 1
                  }：${keyword}：（${list.length}份）</div>`;
                  html += await buildLastWeekTable(list, keyword);
                } catch (error) {
                  this.logger.error(`处理订阅词 ${keyword} 时出错: ${error}`);
                }
              }
            } else {
              html += '<div>无</div>';
            }
            html += `
              <p style="color:#767171;margin-top:20px;">招标公告数据来源：<br>浙江政采网、浙江、四川、重庆、湖北、湖南、安徽、广东、江西、福建、新疆、内蒙古自治区、江苏公共资源交易平台</p>
              <p style="color:#767171;">中标公告数据来源：<br>浙江政采网、浙江、四川、重庆公共资源交易平台</p>
              <hr>
              <p style="color:black;">
                <strong>陈 阳</strong><br>
                华信咨询设计研究院有限公司 创新院<br>
                手机：17818660113
              </p>
            `;
            const subject = `【商机日报】${
              bidList.length + winList.length
            }份新标书待查！您的订阅更新提醒[${yesterday.getFullYear()}-${getDateString(
              yesterday,
            )}]`;
            const mailOptions = {
              from: this.configService.get<string>('EMAIL_SENDER_USER'),
              to: toUser.email,
              subject,
              html,
            };
            await this.transporter.sendMail(mailOptions);
            this.logger.log(
              `邮件发送成功, email: ${toUser.email}, subject: ${subject}`,
            );
          } catch (error) {
            this.logger.error(
              `邮件发送失败, email: ${toUser.email}, error: ${error}`,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(`邮件发送失败, error: ${error}`);
    }
  }

  textToHtml(text) {
    // 将文本按行分割
    const lines = text.trim().split('\n');

    // 用于存储最终的 HTML 内容
    let htmlContent = '';

    lines.forEach((line, index) => {
      // 去除每行的前后空格
      line = line.trim();

      // 定义一个正则表达式来匹配 URL
      const urlPattern = /(https?:\/\/[^\s]+)/g;
      let hasUrl = false;

      // 检查行中是否包含 URL
      if (urlPattern.test(line)) {
        // 如果包含 URL，则先替换 URL 为占位符，避免影响后续处理
        const urlMatches = line.match(urlPattern);
        urlMatches.forEach((url) => {
          line = line.replace(
            url,
            `__URL_PLACEHOLDER__${url}__URL_PLACEHOLDER__`,
          );
          hasUrl = true;
        });
      }

      // 检查是否有冒号
      const colonIndex = line.indexOf(':');
      if (colonIndex !== -1 && !hasUrl) {
        // 分割成两部分：冒号前的部分和冒号后的部分
        const boldPart = line.substring(0, colonIndex);
        const normalPart = line.substring(colonIndex);

        // 将冒号前的部分加粗
        htmlContent += `<strong>${boldPart}</strong>${normalPart}`;
      } else {
        // 如果没有冒号，或者有 URL，则直接保留原样
        htmlContent += line;
      }

      // 如果之前替换了 URL，现在恢复 URL
      if (hasUrl) {
        htmlContent = htmlContent.replace(
          /__URL_PLACEHOLDER__(https?:\/\/[^\s]+)__URL_PLACEHOLDER__/g,
          '$1',
        );
      }

      // 在每一行后添加 <br> 标签，除了最后一行
      if (index < lines.length - 1) {
        htmlContent += '<br>';
      }
    });

    return htmlContent;
  }
}
