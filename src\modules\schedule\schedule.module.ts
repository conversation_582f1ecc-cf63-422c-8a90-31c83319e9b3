import { Module } from '@nestjs/common';
import { ScheduleService } from './schedule.service';
import { AnnouncementModule } from '../announcement/announcement.module';
import { SubscribeModule } from '../subscribe/subscribe.module';
import { UserModule } from '../user/user.module';
import { AreaModule } from '../area/area.module';

@Module({
  imports: [AnnouncementModule, SubscribeModule, UserModule, AreaModule],
  providers: [ScheduleService],
})
export class ScheduleModule {}
