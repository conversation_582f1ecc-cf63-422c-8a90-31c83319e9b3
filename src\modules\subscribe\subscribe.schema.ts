import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class Subscribe extends Document {
  @Prop({ required: true })
  userId: string;

  @Prop()
  areaCode: string;

  @Prop({ required: true, type: [String] })
  keywords: string[];

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop()
  type: string;
}

export const SubscribeSchema = SchemaFactory.createForClass(Subscribe);
