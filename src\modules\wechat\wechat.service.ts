import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class WechatService {
  private corpId: string;
  private appSecret: string;
  private agentId: string;

  constructor(private configService: ConfigService) {
    this.corpId = this.configService.get<string>('WECHAT_CORP_ID');
    this.appSecret = this.configService.get<string>('WECHAT_APP_SECRET');
    this.agentId = this.configService.get<string>('WECHAT_AGENT_ID');
  }

  async getAccessToken(): Promise<string> {
    const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${this.corpId}&corpsecret=${this.appSecret}`;
    const response = await axios.get(url);
    return response.data.access_token;
  }

  async getUserInfo(code: string): Promise<any> {
    const accessToken = await this.getAccessToken();
    const url = `https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=${accessToken}&code=${code}`;
    const response = await axios.get(url);
    return response.data;
  }

  async getUserId(code: string): Promise<string> {
    const userInfo = await this.getUserInfo(code);
    return userInfo.UserId;
  }

  async getUserDetail(userId: string): Promise<any> {
    const accessToken = await this.getAccessToken();
    const url = `https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${accessToken}&userid=${userId}`;
    const response = await axios.get(url);
    return response.data;
  }
}