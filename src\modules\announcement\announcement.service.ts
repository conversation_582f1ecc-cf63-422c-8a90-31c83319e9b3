import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Announcement, AnnouncementDocument } from './announcement.schema';
import { AreaService } from '../area/area.service';
import { DifyService } from '../dify/dify.service';
import { url } from 'inspector';
import { Province } from '../area/province.schema';
import axios from 'axios';
import dayjs from 'dayjs';

@Injectable()
export class AnnouncementService {
  private readonly logger = new Logger('【邮件推送】：');
  constructor(
    @InjectModel(Announcement.name)
    private announcementModel: Model<AnnouncementDocument>,
    private readonly areaService: AreaService,
    private readonly difyService: DifyService,
  ) {}

  /**
   * 格式化日期为 YYYY-MM-DD HH:mm:ss 格式
   * @param date 日期对象
   * @returns 格式化后的日期字符串
   */
  private formatDate(date: Date): string {
    if (!date) return null;

    try {
      // 使用 dayjs 格式化
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    } catch (error) {
      // 如果 dayjs 失败，使用原生 JavaScript 格式化
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }

  async create(announcementDto: any): Promise<Announcement> {
    const area = announcementDto.area;
    const publishDate = new Date(announcementDto.publishDate);
    const areaCode = await this.areaService.getAreaCodeByName(area);

    // 调用 dify 服务提取公告信息
    const difyResult = await this.difyService.extractAnnouncementInfo(
      announcementDto.content,
    );

    // 检查 dify 服务调用是否成功
    if (difyResult.data.status === 'failed') {
      this.logger.error(`Dify 服务调用失败: ${difyResult.data.error}`);
      throw new Error('提取公告信息失败');
    }

    const coreInfo = difyResult.data.outputs.result.core_info;
    // 获取行业信息

    const industry = this.getIndustryByProjectAndUnit(
      coreInfo?.project_name,
      coreInfo?.construction_unit,
    );

    const areaName = await this.areaService.getProvinceCity(areaCode);
    const savedAnnouncement = null;
    if (industry) {
      // 创建公告实体
      const createdAnnouncement = new this.announcementModel({
        ...announcementDto,
        areaName,
        areaCode,
        publishDate,
        projectCode: coreInfo?.project_code,
        projectName: coreInfo?.project_name,
        agencyName: coreInfo?.agency_name,
        constructionUnit: coreInfo?.construction_unit,
        industry: coreInfo?.industry,
        bidDeadline:
          coreInfo?.bid_deadline &&
          !isNaN(new Date(coreInfo.bid_deadline).getTime())
            ? new Date(coreInfo.bid_deadline)
            : undefined,
        tenderEntity: coreInfo?.tender_entity,
        constructionContent: coreInfo?.construction_content || [],
        projectAmount: coreInfo?.budget_amount,
        projectUnit: coreInfo?.budget_unit,
        attachmentName: coreInfo?.attachment_name,
        attachmentUrl: coreInfo?.attachment_url,
        status: announcementDto?.status,
        source: announcementDto?.source,
        provinceName: announcementDto?.province_name,
        tenderAnnouncementLink: announcementDto?.tender_announcement_link,
        tenderAnnouncementName: announcementDto?.tender_announcement_name,
        winningBidder: coreInfo?.winning_bidder,
        winningBidAmount: coreInfo?.winning_bid_amount,
        budgetAmount: coreInfo?.budget_amount,
        budgetUnit: coreInfo?.budget_unit,
      });
      this.logger.log(`公告创建成功: ${createdAnnouncement}`);

      // 保存公告到数据库
      // savedAnnouncement = await createdAnnouncement.save();
    }
    // 推送数据到外部接口
    // try {
    //   await this.pushAnnouncementData(
    //     [savedAnnouncement],
    //     'http://10.9.238.153:8088/tbgl/rest/WBidAnnouncementController/addBidAnnouncement',
    //   );
    //   this.logger.log(`公告数据推送成功: ${savedAnnouncement.title}`);
    // } catch (error) {
    //   this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
    //   // 注意：这里不抛出错误，避免影响公告创建流程
    // }

    return savedAnnouncement;
  }

  async findAll(): Promise<Announcement[]> {
    return this.announcementModel.find().exec();
  }

  async findByUrl(url: string): Promise<Announcement> {
    return this.announcementModel.findOne({ url }).exec();
  }

  async findOne(id: string): Promise<any> {
    const announcement = await this.announcementModel.findById(id).exec();
    if (!announcement) {
      return null;
    }
    return {
      _id: announcement._id,
      title: announcement.title,
      projectName: announcement.projectName,
      content: announcement.content,
      publishDate: announcement.publishDate,
      area: await this.areaService.getAreaNameByCode(announcement.areaCode),
      type: announcement.type,
      url: announcement.url,
      areaCode: announcement.areaCode,
      bidDeadline: announcement.bidDeadline,
      tenderEntity: announcement.tenderEntity,
      constructionContent: announcement.constructionContent,
      budgetAmount: announcement.budgetAmount,
      budgetUnit: announcement.budgetUnit,
    };
  }

  async findAnnouncementsByPublishDate(
    publishDate: Date,
  ): Promise<Announcement[]> {
    return this.announcementModel.find({ publishDate }).exec();
  }

  async findAnnouncementsByConditionDateRange(
    searchWord: string,
    type: string,
    areaCode: string,
    keywords: string[],
    fromDate?: string,
    toDate?: string,
    currentPage?: number,
    pageSize?: number,
  ): Promise<any> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    let searchWordCondition = [];
    if (searchWord) {
      searchWordCondition = [
        {
          title: { $regex: new RegExp(`.*${searchWord}.*`), $options: 'i' },
        },
      ];
    }

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      $or: [...regexConditions, ...searchWordCondition],
      type,
    };

    if (fromDate && toDate) {
      (query as any).publishDate = {
        $gte: new Date(fromDate), // 开始时间（UTC）
        $lte: new Date(toDate), // 结束时间（UTC）
      };
    }

    // 计算跳过多少文档
    const skip = (currentPage - 1) * pageSize;

    // 执行查询并限制返回的文档数量
    const [list, total] = await Promise.all([
      this.announcementModel
        .find(query)
        .sort({ publishDate: -1 })
        .skip(skip)
        .limit(pageSize)
        .exec(),
      this.announcementModel.countDocuments(query).exec(),
    ]);

    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: this.formatDate(announcement.publishDate),
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: this.formatDate(announcement.bidDeadline),
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    // 返回带有分页信息的结果
    return {
      data: res.sort(
        (a, b) =>
          new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime(),
      ),
      currentPage,
      totalPages: Math.ceil(total / pageSize),
      total,
    };
  }

  async findAnnouncementsByCondition(
    areaCode: string,
    keywords: string[],
    publishDate: Date,
    type?: string,
  ): Promise<Announcement[]> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      publishDate,
      $or: regexConditions,
    };

    if (type) {
      (query as any).type = type;
    }
    const list = await this.announcementModel.find(query).exec();
    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: this.formatDate(announcement.publishDate),
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: this.formatDate(announcement.bidDeadline),
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    return res;
  }

  async findAnnouncementsByConditionDateRangeNoPage(
    areaCode: string,
    keywords: string[],
    fromDate: Date,
    toDate: Date,
    type: string,
  ): Promise<Announcement[]> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      $or: regexConditions,
      type,
    };

    (query as any).publishDate = {
      $gte: fromDate, // 开始时间（UTC）
      $lte: toDate, // 结束时间（UTC）
    };

    const list = await this.announcementModel
      .find(query)
      .sort({ publishDate: -1 })
      .exec();
    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: this.formatDate(announcement.publishDate),
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: this.formatDate(announcement.bidDeadline),
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    return res;
  }

  /**
   * 根据项目名称和建设单位获取行业（支持多个行业匹配）
   * @param projectName 项目名称
   * @param constructionUnit 建设单位
   * @returns 行业名称（多个用逗号分割）或undefined
   */
  getIndustryByProjectAndUnit(
    projectName: string,
    constructionUnit: string,
  ): string | undefined {
    // 关键词数组
    const keywords = [
      'epc',
      '工程总承包',
      '设计施工一体化',
      '总包',
      '工程管理',
      '建设总承包',
      '智能化',
      '智能系统',
      '智慧化',
      '智慧',
      '智能技术',
      '自动化',
      '智能控制',
      '信息化',
      '数字化',
      '信息系统建设',
      '信息技术应用',
      'IT化',
      '信息管理',
      '集成',
      '整合',
      '融合',
      '组合',
      '集聚',
      '系统集成',
      '设计',
      '规划',
      '方案设计',
      '方案制定',
      '构思',
      '蓝图绘制',
      '咨询',
      '顾问服务',
      '咨询服务',
      '专业建议',
      '方案咨询',
      '技术咨询',
      '体系',
      '架构',
      '软件系统',
      '管理系统',
      '平台',
      '系统',
      '应用平台',
      '服务平台',
      '操作平台',
      '软件平台',
      '视频',
      '影像',
      '录像',
      '视频监控',
      '视频流',
      '多媒体',
      '监控',
      '监管',
      '监测',
      '观察',
      '控制',
      '监视',
      '储备',
      '备货',
      '库存',
      '储藏',
      '积累',
      '储存',
      '规划',
      '计划',
      '布局',
      '设计方案',
      '筹划',
      '方案制定',
      '改造',
      '升级',
      '更新',
      '改进',
      '整修',
      '重建',
      '建设',
      '施工',
      '建立',
      '开发',
      '打造',
      '建设工程',
      '研究',
      '调查',
      '分析',
      '探索',
      '调研',
      '课题研究',
      '数字化',
      '数字转型',
      '信息数字化',
      '电子化',
      '数字变革',
      '数字技术应用',
    ];

    // 行业配置
    const industries = [
      {
        industry: '军政',
        p0: [
          '人防',
          '数字政府',
          '智慧城市',
          '评审服务',
          '国防科技',
          '军民融合',
          '公共安全',
          '电子政务',
          '治安管理',
          '应急指挥',
          '边防信息化',
          '军事仿真',
          '军民融合产业园',
          '战备工程',
          '国防动员',
          '军事物流',
        ],
        p1: [
          '国防动员',
          '人防',
          '政务云公司',
          '信息中心',
          '大数据局',
          '政数局',
          '经信',
          '财政',
          '税务',
          '公安',
          '检察院',
          '法院',
          '国家安全中心',
          '党校',
          '委员会',
          '办公室',
          '兵团',
          '部队',
          '监狱',
          '国安',
        ],
        p2: ['办', '局', '厅', '司', '发改委', '部'],
      },
      {
        industry: '数据中心',
        p0: [
          '数据中心',
          '算力中心',
          '智算中心',
          '云计算基地',
          '边缘计算节点',
          '绿色数据中心',
          '灾备中心',
          '超算中心',
          '数据湖',
          '大数据平台',
          '冷存储中心',
          '模块化机房',
          '液冷数据中心',
          '东数西算工程',
        ],
        p1: [],
        p2: [],
      },
      {
        industry: '交通能源',
        p0: [
          '智慧多功能杆',
          '新能源充电桩',
          '智能电网',
          '智慧港口',
          '轨道交通大脑',
          '车路协同',
          '能源互联网平台',
          '综合管廊系统',
          '碳排放监测',
          '油气管道监控',
          '路网感知系统',
          '智慧停车系统',
          '无人机',
          '低空',
        ],
        p1: [
          '能源局',
          '交通运输局',
          '电网',
          '供电',
          '水利',
          '水务',
          '水电',
          '电力',
          '电厂',
          '发电',
          '供水',
          '自来水',
          '交通',
          '石化',
          '石油',
          '铁路',
          '运输',
          '高铁',
          '港口',
          '低空',
          '光伏',
          '新能源',
          '双碳',
          '航运',
          '机场',
        ],
        p2: [],
      },
      {
        industry: '医卫',
        p0: [
          '人社公积金',
          '养老',
          '医保',
          '卫健',
          '智慧医院',
          '远程医疗平台',
          '电子病历系统',
          '区域卫生平台',
          '医药监管追溯',
          '疾控信息化',
          '基因数据中心',
          'AI辅助诊断',
          '医疗物联网',
          '医共体平台',
          '电子健康卡',
        ],
        p1: [
          '养老',
          '医院',
          '卫生',
          '健康',
          '疾病',
          '医保',
          '医疗',
          '保健',
          '预防',
          '诊断',
          '诊所',
        ],
        p2: [],
      },
      {
        industry: '园区',
        p0: [
          '社区',
          '楼宇',
          '博物馆',
          '文化场馆',
          '体育场馆',
          '档案馆',
          '会议会展中心',
          '景区',
          '文旅',
          '产业园',
          '基地',
          '校园',
          '产业孵化器',
          '科技园区',
          '物流园区',
          '保税园区',
          '主题公园',
          '商业综合体',
          '农业示范园',
          '工业园区',
          '自贸试验区',
          '创业园区',
          '产城融合示范区',
        ],
        p1: [
          '文化场馆',
          '体育场馆',
          '档案馆',
          '博物馆',
          '学校',
          '大学',
          '中学',
          '小学',
          '学院',
          '园区',
          '社区',
          '管理委员会',
          '教育',
        ],
        p2: [],
      },
      {
        industry: '企业大客户',
        p0: ['企业云', '国资云', '数字化转型', '物资', '粮仓', '粮食', '粮储'],
        p1: [
          '住建局',
          '城建局',
          '管道公司',
          '粮食',
          '粮储',
          '粮油',
          '物资',
          '工厂',
          '地产',
          '商业',
          '城投',
          '银行',
          '烟草',
        ],
        p2: ['中心', '公司', '集团'],
      },
      {
        industry: '网信安',
        p0: [
          '专用通信',
          '应急管理',
          '消防',
          '反诈',
          '信息防范',
          '信创',
          'XC',
          '互联网骨干直联点',
          '漏洞',
          '安全防控',
          '网络安全靶场',
          '数据安全治理',
          '密码技术应用',
          '等级保护2.0',
          '安全审计系统',
          '威胁情报平台',
          '攻防演练平台',
          '安全运营中心（SOC）',
          '零信任架构',
          '区块链安全',
          'APP隐私合规检测',
          '云安全防护',
          'APT防御系统',
        ],
        p1: [
          '工信部',
          '通管局',
          '网信办',
          '应急管理局',
          '中国信通院',
          '国家网络安全中心',
        ],
        p2: ['通信', '信息', '计算机', '网络'],
      },
    ];

    // 第一步：检查项目名称是否包含关键词
    const projectNameLower = projectName?.toLowerCase() || '';
    const hasKeyword = keywords.some((keyword) =>
      projectNameLower.includes(keyword.toLowerCase()),
    );

    // 第二步：检查项目名称是否在任何行业的p0中
    let foundInP0 = false;
    for (const industry of industries) {
      const matchedInP0 = industry.p0.some((p0Item) =>
        projectNameLower.includes(p0Item.toLowerCase()),
      );
      if (matchedInP0) {
        foundInP0 = true;
        break;
      }
    }

    // 如果关键词数组和p0里都未查到，返回undefined
    if (!hasKeyword && !foundInP0) {
      return undefined;
    }

    // 第三步：通过项目名称去p0匹配行业（支持多个匹配）
    const matchedIndustries: string[][] = [[], [], []];
    const constructionUnitLower = constructionUnit?.toLowerCase() || '';

    for (const industry of industries) {
      for (let i = 0; i < 3; i++) {
        let matched = false;
        if (i == 0) {
          matched = industry[`p${i}`].some((item) =>
            projectNameLower.includes(item.toLowerCase()),
          );
        } else {
          matched = industry[`p${i}`].some((item) =>
            constructionUnitLower.includes(item.toLowerCase()),
          );
        }
        if (matched) {
          matchedIndustries[i].push(industry.industry);
          break;
        }
      }
    }
    const finalIndustries = [
      ...matchedIndustries[0],
      ...matchedIndustries[1],
      ...matchedIndustries[2],
    ];
    // 如果通过建设单位匹配到了行业，返回匹配结果
    if (finalIndustries.length > 0) {
      return finalIndustries.join(',');
    }

    // p1,p2未匹配到则是其他
    return '其他';
  }

  /**
   * 推送公告数据到外部接口
   * @param announcements 公告数据数组
   */
  async pushAnnouncementData(announcements: Announcement[]): Promise<void> {
    if (!announcements || announcements.length === 0) {
      this.logger.warn('没有需要推送的公告数据');
      return;
    }
    // const pushUrl =
    //   'http://10.9.238.153:8088/tbgl/rest/WBusinessCollect/saveInfo';
    const pushUrl = 'http://10.13.4.66:8054/rest/WBusinessCollect/saveInfo';

    // 构建推送数据格式 - List<Object>
    const pushData = announcements.map((announcement) => ({
      title: announcement.title,
      projectName: announcement.projectName,
      projectCode: announcement.projectCode,
      agencyName: announcement.agencyName,
      constructionUnit: announcement.constructionUnit,
      industry: announcement.industry,
      content: announcement.content,
      publishDate: this.formatDate(announcement.publishDate),
      provinceName: announcement.provinceName,
      url: announcement.url,
      bidDeadline: this.formatDate(announcement.bidDeadline),
      tenderEntity: announcement.tenderEntity,
      projectAmount: announcement.projectAmount,
      projectUnit: announcement.projectUnit,
      attachmentName: announcement.attachmentName,
      attachmentUrl: announcement.attachmentUrl,
      status: announcement.status,
      source: announcement.source,
    }));

    this.logger.log(`准备推送 ${pushData.length} 条公告数据`);
    // this.logger.log(`推送数据: ${JSON.stringify(pushData)}`);
    // 发送 POST 请求
    const response = await axios.post(pushUrl, pushData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10秒超时
    });
    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`推送失败，HTTP状态码: ${response.status}`);
    }

    this.logger.log(`推送响应: ${JSON.stringify(response.data)}`);
  }
  async pushAnnouncementDataOfZhongbiao(
    announcements: Announcement[],
  ): Promise<void> {
    if (!announcements || announcements.length === 0) {
      this.logger.warn('没有需要推送的公告数据');
      return;
    }
    // const pushUrl =
    //   'http://10.9.238.153:8088/tbgl/rest/WBidAnnouncementController/addBidAnnouncement';
    const pushUrl =
      'http://10.13.4.66:8054/rest/WBidAnnouncementController/addBidAnnouncement';
    // 构建推送数据格式 - List<Object>
    const pushData = announcements.map((announcement) => ({
      title: announcement.title,
      projectName: announcement.projectName,
      projectCode: announcement.projectCode,
      agencyName: announcement.agencyName,
      constructionUnit: announcement.constructionUnit,
      industry: announcement.industry,
      content: announcement.content,
      publishDate: this.formatDate(announcement.publishDate),
      provinceName: announcement.provinceName,
      url: announcement.url,
      bidDeadline: this.formatDate(announcement.bidDeadline),
      tenderEntity: announcement.tenderEntity,
      projectAmount: announcement.projectAmount,
      projectUnit: announcement.projectUnit,
      attachmentName: announcement.attachmentName,
      attachmentUrl: announcement.attachmentUrl,
      status: announcement.status,
      source: announcement.source,
      tenderAnnouncementLink: announcement.tenderAnnouncementLink,
      tenderAnnouncementName: announcement.tenderAnnouncementName,
      winningBidder: announcement.winningBidder,
      winningBidAmount: announcement.winningBidAmount,
    }));

    this.logger.log(`准备推送 ${pushData.length} 条公告数据`);
    // this.logger.log(`推送数据: ${JSON.stringify(pushData)}`);
    // 发送 POST 请求
    const response = await axios.post(pushUrl, pushData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10秒超时
    });
    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`推送失败，HTTP状态码: ${response.status}`);
    }

    this.logger.log(`推送响应: ${JSON.stringify(response.data)}`);
  }

  async pushAnnouncementDataOfWengshu(
    announcements: Announcement[],
  ): Promise<void> {
    if (!announcements || announcements.length === 0) {
      this.logger.warn('没有需要推送的公告数据');
      return;
    }
    // const pushUrl =
    //   'http://10.9.238.153:8088/tbgl/rest/WBusinessCollect/saveInfo';
    const pushUrl = 'http://10.9.238.100:8010/api/myvanna/mongo2sql';

    // 构建推送数据格式 - List<Object>
    const pushData = announcements.map((announcement) => ({
      title: announcement.title,
      projectName: announcement.projectName,
      projectCode: announcement.projectCode,
      agencyName: announcement.agencyName,
      constructionUnit: announcement.constructionUnit,
      industry: announcement.industry,
      content: announcement.content,
      publishDate: this.formatDate(announcement.publishDate),
      provinceName: announcement.provinceName,
      url: announcement.url,
      bidDeadline: this.formatDate(announcement.bidDeadline),
      tenderEntity: announcement.tenderEntity,
      projectAmount: announcement.projectAmount,
      projectUnit: announcement.projectUnit,
      attachmentName: announcement.attachmentName,
      attachmentUrl: announcement.attachmentUrl,
      status: announcement.status,
      source: announcement.source,
      tenderAnnouncementLink: announcement.tenderAnnouncementLink,
      tenderAnnouncementName: announcement.tenderAnnouncementName,
      winningBidder: announcement.winningBidder,
      winningBidAmount: announcement.winningBidAmount,
    }));

    this.logger.log(`准备推送 ${pushData.length} 条公告数据`);
    // this.logger.log(`推送数据: ${JSON.stringify(pushData)}`);
    // 发送 POST 请求
    const response = await axios.post(pushUrl, pushData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10秒超时
    });
    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`推送失败，HTTP状态码: ${response.status}`);
    }

    this.logger.log(`推送响应: ${JSON.stringify(response.data)}`);
  }
  /**
   * 根据发布日期、状态和类型查询公告并推送到远程
   * @param publishDate 发布日期 (YYYY-MM-DD 格式)
   * @param status 状态 (可以是字符串或字符串数组)
   * @param type 类型
   * @returns 推送结果
   */
  async queryAndPushAnnouncements(
    publishDate?: string,
    type?: string,
    status?: string | string[],
  ): Promise<{
    success: boolean;
    total: number;
    pushed: number;
    message: string;
  }> {
    try {
      // 构建查询条件
      const query: any = {};

      // 添加发布日期条件
      if (publishDate) {
        const startDate = new Date(publishDate);
        const endDate = new Date(publishDate);
        endDate.setDate(endDate.getDate() + 1); // 下一天的开始时间

        query.publishDate = {
          $gte: startDate,
          $lt: endDate,
        };
      }

      // 添加状态条件
      if (status) {
        if (Array.isArray(status)) {
          // 如果是数组，使用 $in 操作符
          query.status = { $in: status };
        } else {
          // 如果是字符串，直接匹配
          query.status = status;
        }
      }

      query.type = '2';

      this.logger.log(`查询条件: ${JSON.stringify(query)}`);

      // 查询数据库
      const announcements = await this.announcementModel.find(query).exec();

      this.logger.log(`查询到 ${announcements.length} 条公告数据`);

      if (announcements.length === 0) {
        return {
          success: true,
          total: 0,
          pushed: 0,
          message: '没有找到符合条件的公告数据',
        };
      }

      // 推送数据到远程
      try {
        // if (type == '1') {
        //   await this.pushAnnouncementData(announcements);
        // }
        // if (type == '2') {
        //   await this.pushAnnouncementDataOfZhongbiao(announcements);
        // }
        if (type == '3') {
          await this.pushAnnouncementDataOfWengshu(announcements);
        }
        return {
          success: true,
          total: announcements.length,
          pushed: announcements.length,
          message: `成功推送 ${announcements.length} 条公告数据`,
        };
      } catch (pushError) {
        this.logger.error(`推送失败: ${pushError.message}`, pushError.stack);

        return {
          success: false,
          total: announcements.length,
          pushed: 0,
          message: `查询成功但推送失败: ${pushError.message}`,
        };
      }
    } catch (error) {
      this.logger.error(`查询和推送失败: ${error.message}`, error.stack);

      return {
        success: false,
        total: 0,
        pushed: 0,
        message: `操作失败: ${error.message}`,
      };
    }
  }

  /**
   * 根据日期范围查询公告并推送到远程
   * @param fromDate 开始日期 (YYYY-MM-DD 格式)
   * @param toDate 结束日期 (YYYY-MM-DD 格式)
   * @param status 状态 (可以是字符串或字符串数组)
   * @param type 类型 (可选)
   * @returns 推送结果
   */
  async queryAndPushAnnouncementsByDateRange(
    fromDate: string,
    toDate: string,
    type?: string,
    status?: string | string[],
  ): Promise<{
    success: boolean;
    total: number;
    pushed: number;
    message: string;
  }> {
    try {
      // 构建查询条件
      const query: any = {};

      // 添加日期范围条件
      const startDate = new Date(fromDate);
      const endDate = new Date(toDate);
      endDate.setDate(endDate.getDate() + 1); // 包含结束日期当天

      query.publishDate = {
        $gte: startDate,
        $lt: endDate,
      };

      // 添加状态条件
      if (status) {
        if (Array.isArray(status)) {
          // 如果是数组，使用 $in 操作符
          query.status = { $in: status };
        } else {
          // 如果是字符串，直接匹配
          query.status = status;
        }
      }

      // 添加类型条件
      if (type) {
        query.type = type;
      }

      this.logger.log(`日期范围查询条件: ${JSON.stringify(query)}`);

      // 查询数据库
      const announcements = await this.announcementModel.find(query).exec();

      this.logger.log(`查询到 ${announcements.length} 条公告数据`);

      if (announcements.length === 0) {
        return {
          success: true,
          total: 0,
          pushed: 0,
          message: '没有找到符合条件的公告数据',
        };
      }

      // 推送数据到远程
      try {
        if (type == '1') {
          await this.pushAnnouncementData(announcements);
        }
        if (type == '2') {
          await this.pushAnnouncementDataOfZhongbiao(announcements);
        }

        return {
          success: true,
          total: announcements.length,
          pushed: announcements.length,
          message: `成功推送 ${announcements.length} 条公告数据`,
        };
      } catch (pushError) {
        this.logger.error(`推送失败: ${pushError.message}`, pushError.stack);

        return {
          success: false,
          total: announcements.length,
          pushed: 0,
          message: `查询成功但推送失败: ${pushError.message}`,
        };
      }
    } catch (error) {
      this.logger.error(`查询和推送失败: ${error.message}`, error.stack);

      return {
        success: false,
        total: 0,
        pushed: 0,
        message: `操作失败: ${error.message}`,
      };
    }
  }

  // async sendAnnouncementsByCondition(
  //   areaCode: string,
  //   keywords: string[],
  //   publishDate: Date,
  // ): Promise<void> {
  //   const subscribes = await this.subscribeService.findByAreaCodeAndKeywords(
  //     areaCode,
  //     keywords,
  //   );

  //   if (subscribes.length === 0) {
  //     console.log('No subscribes found for the given criteria.');
  //     return;
  //   }

  //   const announcements = await this.announcementModel
  //     .find({ areaCode, keywords: { $in: keywords } })
  //     .exec();

  //   if (announcements.length === 0) {
  //     console.log('No announcements found for the given criteria.');
  //     return;
  //   }

  //   const transporter = nodemailer.createTransport({
  //     service: 'gmail',
  //     auth: {
  //       user: '<EMAIL>',
  //       pass: 'your-email-password',
  //     },
  //   });

  //   for (const subscribe of subscribes) {
  //     for (const announcement of announcements) {
  //       const mailOptions = {
  //         from: '<EMAIL>',
  //         to: subscribe.email,
  //         subject: announcement.title,
  //         text: announcement.content,
  //       };

  //       await transporter.sendMail(mailOptions);
  //       console.log(
  //         `Email sent to ${subscribe.email} for announcement: ${announcement.title}`,
  //       );
  //     }
  //   }
  // }
}
