import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Announcement, AnnouncementDocument } from './announcement.schema';
import { AreaService } from '../area/area.service';
import { DifyService } from '../dify/dify.service';
import { url } from 'inspector';
import { Province } from '../area/province.schema';
import axios from 'axios';
import dayjs from 'dayjs';

@Injectable()
export class AnnouncementService {
  private readonly logger = new Logger('【邮件推送】：');
  constructor(
    @InjectModel(Announcement.name)
    private announcementModel: Model<AnnouncementDocument>,
    private readonly areaService: AreaService,
    private readonly difyService: DifyService,
  ) {}
  async create(announcementDto: any): Promise<Announcement> {
    const area = announcementDto.area;
    const publishDate = new Date(announcementDto.publishDate);
    const areaCode = await this.areaService.getAreaCodeByName(area);

    // 调用 dify 服务提取公告信息
    const difyResult = await this.difyService.extractAnnouncementInfo(
      announcementDto.content,
    );

    // 检查 dify 服务调用是否成功
    if (difyResult.data.status === 'failed') {
      this.logger.error(`Dify 服务调用失败: ${difyResult.data.error}`);
      throw new Error('提取公告信息失败');
    }

    const coreInfo = difyResult.data.outputs.result.core_info;

    // 创建公告实体
    const createdAnnouncement = new this.announcementModel({
      ...announcementDto,
      areaCode,
      publishDate,
      projectCode: coreInfo?.project_code,
      projectName: coreInfo?.project_name,
      agencyName: coreInfo?.agency_name,
      constructionUnit: coreInfo?.construction_unit,
      industry: coreInfo?.industry,
      bidDeadline:
        coreInfo?.bid_deadline &&
        !isNaN(new Date(coreInfo.bid_deadline).getTime())
          ? new Date(coreInfo.bid_deadline)
          : undefined,
      tenderEntity: coreInfo?.tender_entity,
      constructionContent: coreInfo?.construction_content || [],
      budgetAmount: coreInfo?.budget_amount?.value,
      budgetUnit: coreInfo?.budget_amount?.unit,
      projectAmount: coreInfo?.budget_amount,
      projectUnit: coreInfo?.budget_unit,
      attachmentName: coreInfo?.attachment_name,
      attachmentUrl: coreInfo?.attachment_url,
      status: announcementDto?.status,
      source: announcementDto?.source,
    });

    // 保存公告到数据库
    const savedAnnouncement = await createdAnnouncement.save();

    // 推送数据到外部接口
    try {
      await this.pushAnnouncementData(savedAnnouncement);
      this.logger.log(`公告数据推送成功: ${savedAnnouncement.title}`);
    } catch (error) {
      this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
      // 注意：这里不抛出错误，避免影响公告创建流程
    }

    return savedAnnouncement;
  }

  async findAll(): Promise<Announcement[]> {
    return this.announcementModel.find().exec();
  }

  async findByUrl(url: string): Promise<Announcement> {
    return this.announcementModel.findOne({ url }).exec();
  }

  async findOne(id: string): Promise<any> {
    const announcement = await this.announcementModel.findById(id).exec();
    if (!announcement) {
      return null;
    }
    return {
      _id: announcement._id,
      title: announcement.title,
      projectName: announcement.projectName,
      content: announcement.content,
      publishDate: announcement.publishDate,
      area: await this.areaService.getAreaNameByCode(announcement.areaCode),
      type: announcement.type,
      url: announcement.url,
      areaCode: announcement.areaCode,
      bidDeadline: announcement.bidDeadline,
      tenderEntity: announcement.tenderEntity,
      constructionContent: announcement.constructionContent,
      budgetAmount: announcement.budgetAmount,
      budgetUnit: announcement.budgetUnit,
    };
  }

  async findAnnouncementsByPublishDate(
    publishDate: Date,
  ): Promise<Announcement[]> {
    return this.announcementModel.find({ publishDate }).exec();
  }

  async findAnnouncementsByConditionDateRange(
    searchWord: string,
    type: string,
    areaCode: string,
    keywords: string[],
    fromDate?: string,
    toDate?: string,
    currentPage?: number,
    pageSize?: number,
  ): Promise<any> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    let searchWordCondition = [];
    if (searchWord) {
      searchWordCondition = [
        {
          title: { $regex: new RegExp(`.*${searchWord}.*`), $options: 'i' },
        },
      ];
    }

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      $or: [...regexConditions, ...searchWordCondition],
      type,
    };

    if (fromDate && toDate) {
      (query as any).publishDate = {
        $gte: new Date(fromDate), // 开始时间（UTC）
        $lte: new Date(toDate), // 结束时间（UTC）
      };
    }

    // 计算跳过多少文档
    const skip = (currentPage - 1) * pageSize;

    // 执行查询并限制返回的文档数量
    const [list, total] = await Promise.all([
      this.announcementModel
        .find(query)
        .sort({ publishDate: -1 })
        .skip(skip)
        .limit(pageSize)
        .exec(),
      this.announcementModel.countDocuments(query).exec(),
    ]);

    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: announcement.publishDate,
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: announcement.bidDeadline,
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    // 返回带有分页信息的结果
    return {
      data: res.sort(
        (a, b) => b.publishDate.getTime() - a.publishDate.getTime(),
      ),
      currentPage,
      totalPages: Math.ceil(total / pageSize),
      total,
    };
  }

  async findAnnouncementsByCondition(
    areaCode: string,
    keywords: string[],
    publishDate: Date,
    type?: string,
  ): Promise<Announcement[]> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      publishDate,
      $or: regexConditions,
    };

    if (type) {
      (query as any).type = type;
    }
    const list = await this.announcementModel.find(query).exec();
    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: announcement.publishDate,
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: announcement.bidDeadline,
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    return res;
  }

  async findAnnouncementsByConditionDateRangeNoPage(
    areaCode: string,
    keywords: string[],
    fromDate: Date,
    toDate: Date,
    type: string,
  ): Promise<Announcement[]> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      $or: regexConditions,
      type,
    };

    (query as any).publishDate = {
      $gte: fromDate, // 开始时间（UTC）
      $lte: toDate, // 结束时间（UTC）
    };

    const list = await this.announcementModel
      .find(query)
      .sort({ publishDate: -1 })
      .exec();
    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: announcement.publishDate,
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: announcement.bidDeadline,
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    return res;
  }

  /**
   * 推送公告数据到外部接口
   * @param announcement 公告数据
   */
  private async pushAnnouncementData(
    announcement: Announcement,
  ): Promise<void> {
    const pushUrl =
      'http://10.9.238.153:8088/tbgl/rest/WBusinessCollect/saveInfo';

    // 构建推送数据格式 - 包装成数组格式 List<Object>
    const pushData = [
      {
        id: announcement._id?.toString(),
        title: announcement.title,
        projectName: announcement.projectName,
        projectCode: announcement.projectCode,
        agencyName: announcement.agencyName,
        constructionUnit: announcement.constructionUnit,
        industry: announcement.industry,
        content: announcement.content,
        publishDate: announcement.publishDate
          ? dayjs(announcement.publishDate).format('YYYY-MM-DD HH:mm:ss')
          : '',
        type: announcement.type,
        areaCode: announcement.areaCode,
        url: announcement.url,
        bidDeadline: announcement.bidDeadline,
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
        projectAmount: announcement.projectAmount,
        projectUnit: announcement.projectUnit,
        attachmentName: announcement.attachmentName,
        attachmentUrl: announcement.attachmentUrl,
        status: announcement.status,
        source: announcement.source,
      },
    ];
    // 发送 POST 请求
    const response = await axios.post(pushUrl, pushData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10秒超时
    });

    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`推送失败，HTTP状态码: ${response.status}`);
    }

    this.logger.log(`推送响应: ${JSON.stringify(response.data)}`);
  }

  // async sendAnnouncementsByCondition(
  //   areaCode: string,
  //   keywords: string[],
  //   publishDate: Date,
  // ): Promise<void> {
  //   const subscribes = await this.subscribeService.findByAreaCodeAndKeywords(
  //     areaCode,
  //     keywords,
  //   );

  //   if (subscribes.length === 0) {
  //     console.log('No subscribes found for the given criteria.');
  //     return;
  //   }

  //   const announcements = await this.announcementModel
  //     .find({ areaCode, keywords: { $in: keywords } })
  //     .exec();

  //   if (announcements.length === 0) {
  //     console.log('No announcements found for the given criteria.');
  //     return;
  //   }

  //   const transporter = nodemailer.createTransport({
  //     service: 'gmail',
  //     auth: {
  //       user: '<EMAIL>',
  //       pass: 'your-email-password',
  //     },
  //   });

  //   for (const subscribe of subscribes) {
  //     for (const announcement of announcements) {
  //       const mailOptions = {
  //         from: '<EMAIL>',
  //         to: subscribe.email,
  //         subject: announcement.title,
  //         text: announcement.content,
  //       };

  //       await transporter.sendMail(mailOptions);
  //       console.log(
  //         `Email sent to ${subscribe.email} for announcement: ${announcement.title}`,
  //       );
  //     }
  //   }
  // }
}
