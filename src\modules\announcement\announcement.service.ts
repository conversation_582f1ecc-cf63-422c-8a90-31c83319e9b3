import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Announcement, AnnouncementDocument } from './announcement.schema';
import { AreaService } from '../area/area.service';
import { DifyService } from '../dify/dify.service';
import { url } from 'inspector';
import { Province } from '../area/province.schema';
import axios from 'axios';
import dayjs from 'dayjs';

@Injectable()
export class AnnouncementService {
  private readonly logger = new Logger('【邮件推送】：');
  constructor(
    @InjectModel(Announcement.name)
    private announcementModel: Model<AnnouncementDocument>,
    private readonly areaService: AreaService,
    private readonly difyService: DifyService,
  ) {}

  /**
   * 格式化日期为 YYYY-MM-DD HH:mm:ss 格式
   * @param date 日期对象
   * @returns 格式化后的日期字符串
   */
  private formatDate(date: Date): string {
    if (!date) return null;

    try {
      // 使用 dayjs 格式化
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    } catch (error) {
      // 如果 dayjs 失败，使用原生 JavaScript 格式化
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }

  /**
   * 获取商机对应行业
   * @param  {string} projectName // 项目名称
   * @param  {string} businessName //建设单位
   * @returns string
   */
  getIndustry;
  async create(announcementDto: any): Promise<Announcement> {
    const area = announcementDto.area;
    const publishDate = new Date(announcementDto.publishDate);
    const areaCode = await this.areaService.getAreaCodeByName(area);

    // 调用 dify 服务提取公告信息
    const difyResult = await this.difyService.extractAnnouncementInfo(
      announcementDto.content,
    );

    // 检查 dify 服务调用是否成功
    if (difyResult.data.status === 'failed') {
      this.logger.error(`Dify 服务调用失败: ${difyResult.data.error}`);
      throw new Error('提取公告信息失败');
    }

    const coreInfo = difyResult.data.outputs.result.core_info;
    // 获取行业信息
    const industry = await this.getIndustry(
      coreInfo?.project_name,
      coreInfo?.construction_unit,
    );
    // 创建公告实体
    const createdAnnouncement = new this.announcementModel({
      ...announcementDto,
      areaCode,
      publishDate,
      projectCode: coreInfo?.project_code,
      projectName: coreInfo?.project_name,
      agencyName: coreInfo?.agency_name,
      constructionUnit: coreInfo?.construction_unit,
      industry: coreInfo?.industry,
      bidDeadline:
        coreInfo?.bid_deadline &&
        !isNaN(new Date(coreInfo.bid_deadline).getTime())
          ? new Date(coreInfo.bid_deadline)
          : undefined,
      tenderEntity: coreInfo?.tender_entity,
      constructionContent: coreInfo?.construction_content || [],
      projectAmount: coreInfo?.budget_amount,
      projectUnit: coreInfo?.budget_unit,
      attachmentName: coreInfo?.attachment_name,
      attachmentUrl: coreInfo?.attachment_url,
      status: announcementDto?.status,
      source: announcementDto?.source,
      provinceName: announcementDto?.province_name,
      tenderAnnouncementLink: announcementDto?.tender_announcement_link,
      tenderAnnouncementName: announcementDto?.tender_announcement_name,
      winningBidder: coreInfo?.winning_bidder,
      winningBidAmount: coreInfo?.winning_bid_amount,
      budgetAmount: coreInfo?.budget_amount,
      budgetUnit: coreInfo?.budget_unit,
    });

    // 保存公告到数据库
    const savedAnnouncement = await createdAnnouncement.save();

    // 推送数据到外部接口
    // try {
    //   await this.pushAnnouncementData(
    //     [savedAnnouncement],
    //     'http://10.9.238.153:8088/tbgl/rest/WBidAnnouncementController/addBidAnnouncement',
    //   );
    //   this.logger.log(`公告数据推送成功: ${savedAnnouncement.title}`);
    // } catch (error) {
    //   this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
    //   // 注意：这里不抛出错误，避免影响公告创建流程
    // }

    return savedAnnouncement;
  }

  async findAll(): Promise<Announcement[]> {
    return this.announcementModel.find().exec();
  }

  async findByUrl(url: string): Promise<Announcement> {
    return this.announcementModel.findOne({ url }).exec();
  }

  async findOne(id: string): Promise<any> {
    const announcement = await this.announcementModel.findById(id).exec();
    if (!announcement) {
      return null;
    }
    return {
      _id: announcement._id,
      title: announcement.title,
      projectName: announcement.projectName,
      content: announcement.content,
      publishDate: announcement.publishDate,
      area: await this.areaService.getAreaNameByCode(announcement.areaCode),
      type: announcement.type,
      url: announcement.url,
      areaCode: announcement.areaCode,
      bidDeadline: announcement.bidDeadline,
      tenderEntity: announcement.tenderEntity,
      constructionContent: announcement.constructionContent,
      budgetAmount: announcement.budgetAmount,
      budgetUnit: announcement.budgetUnit,
    };
  }

  async findAnnouncementsByPublishDate(
    publishDate: Date,
  ): Promise<Announcement[]> {
    return this.announcementModel.find({ publishDate }).exec();
  }

  async findAnnouncementsByConditionDateRange(
    searchWord: string,
    type: string,
    areaCode: string,
    keywords: string[],
    fromDate?: string,
    toDate?: string,
    currentPage?: number,
    pageSize?: number,
  ): Promise<any> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    let searchWordCondition = [];
    if (searchWord) {
      searchWordCondition = [
        {
          title: { $regex: new RegExp(`.*${searchWord}.*`), $options: 'i' },
        },
      ];
    }

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      $or: [...regexConditions, ...searchWordCondition],
      type,
    };

    if (fromDate && toDate) {
      (query as any).publishDate = {
        $gte: new Date(fromDate), // 开始时间（UTC）
        $lte: new Date(toDate), // 结束时间（UTC）
      };
    }

    // 计算跳过多少文档
    const skip = (currentPage - 1) * pageSize;

    // 执行查询并限制返回的文档数量
    const [list, total] = await Promise.all([
      this.announcementModel
        .find(query)
        .sort({ publishDate: -1 })
        .skip(skip)
        .limit(pageSize)
        .exec(),
      this.announcementModel.countDocuments(query).exec(),
    ]);

    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: this.formatDate(announcement.publishDate),
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: this.formatDate(announcement.bidDeadline),
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    // 返回带有分页信息的结果
    return {
      data: res.sort(
        (a, b) =>
          new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime(),
      ),
      currentPage,
      totalPages: Math.ceil(total / pageSize),
      total,
    };
  }

  async findAnnouncementsByCondition(
    areaCode: string,
    keywords: string[],
    publishDate: Date,
    type?: string,
  ): Promise<Announcement[]> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      publishDate,
      $or: regexConditions,
    };

    if (type) {
      (query as any).type = type;
    }
    const list = await this.announcementModel.find(query).exec();
    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: this.formatDate(announcement.publishDate),
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: this.formatDate(announcement.bidDeadline),
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    return res;
  }

  async findAnnouncementsByConditionDateRangeNoPage(
    areaCode: string,
    keywords: string[],
    fromDate: Date,
    toDate: Date,
    type: string,
  ): Promise<Announcement[]> {
    // 构建正则表达式数组
    const regexConditions = keywords.map((keyword) => ({
      title: { $regex: new RegExp(`.*${keyword}.*`), $options: 'i' },
    }));

    // 构建最终查询条件
    const query = {
      areaCode: { $regex: `^${areaCode}`, $options: 'i' },
      $or: regexConditions,
      type,
    };

    (query as any).publishDate = {
      $gte: fromDate, // 开始时间（UTC）
      $lte: toDate, // 结束时间（UTC）
    };

    const list = await this.announcementModel
      .find(query)
      .sort({ publishDate: -1 })
      .exec();
    const res = [];
    for (const announcement of list) {
      res.push({
        _id: announcement._id,
        title: announcement.title,
        projectName: announcement.projectName,
        content: announcement.content,
        publishDate: this.formatDate(announcement.publishDate),
        area: await this.areaService.getAreaNameByCode(announcement.areaCode),
        type: announcement.type,
        url: announcement.url,
        areaCode: announcement.areaCode,
        bidDeadline: this.formatDate(announcement.bidDeadline),
        tenderEntity: announcement.tenderEntity,
        constructionContent: announcement.constructionContent,
        budgetAmount: announcement.budgetAmount,
        budgetUnit: announcement.budgetUnit,
      });
    }
    return res;
  }

  /**
   * 推送公告数据到外部接口
   * @param announcements 公告数据数组
   */
  async pushAnnouncementData(announcements: Announcement[]): Promise<void> {
    if (!announcements || announcements.length === 0) {
      this.logger.warn('没有需要推送的公告数据');
      return;
    }
    // const pushUrl =
    //   'http://10.9.238.153:8088/tbgl/rest/WBusinessCollect/saveInfo';
    const pushUrl = 'http://10.13.4.66:8054/rest/WBusinessCollect/saveInfo';

    // 构建推送数据格式 - List<Object>
    const pushData = announcements.map((announcement) => ({
      title: announcement.title,
      projectName: announcement.projectName,
      projectCode: announcement.projectCode,
      agencyName: announcement.agencyName,
      constructionUnit: announcement.constructionUnit,
      industry: announcement.industry,
      content: announcement.content,
      publishDate: this.formatDate(announcement.publishDate),
      provinceName: announcement.provinceName,
      url: announcement.url,
      bidDeadline: this.formatDate(announcement.bidDeadline),
      tenderEntity: announcement.tenderEntity,
      projectAmount: announcement.projectAmount,
      projectUnit: announcement.projectUnit,
      attachmentName: announcement.attachmentName,
      attachmentUrl: announcement.attachmentUrl,
      status: announcement.status,
      source: announcement.source,
    }));

    this.logger.log(`准备推送 ${pushData.length} 条公告数据`);
    // this.logger.log(`推送数据: ${JSON.stringify(pushData)}`);
    // 发送 POST 请求
    const response = await axios.post(pushUrl, pushData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10秒超时
    });
    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`推送失败，HTTP状态码: ${response.status}`);
    }

    this.logger.log(`推送响应: ${JSON.stringify(response.data)}`);
  }
  async pushAnnouncementDataOfZhongbiao(
    announcements: Announcement[],
  ): Promise<void> {
    if (!announcements || announcements.length === 0) {
      this.logger.warn('没有需要推送的公告数据');
      return;
    }
    // const pushUrl =
    //   'http://10.9.238.153:8088/tbgl/rest/WBidAnnouncementController/addBidAnnouncement';
    const pushUrl =
      'http://10.13.4.66:8054/rest/WBidAnnouncementController/addBidAnnouncement';
    // 构建推送数据格式 - List<Object>
    const pushData = announcements.map((announcement) => ({
      title: announcement.title,
      projectName: announcement.projectName,
      projectCode: announcement.projectCode,
      agencyName: announcement.agencyName,
      constructionUnit: announcement.constructionUnit,
      industry: announcement.industry,
      content: announcement.content,
      publishDate: this.formatDate(announcement.publishDate),
      provinceName: announcement.provinceName,
      url: announcement.url,
      bidDeadline: this.formatDate(announcement.bidDeadline),
      tenderEntity: announcement.tenderEntity,
      projectAmount: announcement.projectAmount,
      projectUnit: announcement.projectUnit,
      attachmentName: announcement.attachmentName,
      attachmentUrl: announcement.attachmentUrl,
      status: announcement.status,
      source: announcement.source,
      tenderAnnouncementLink: announcement.tenderAnnouncementLink,
      tenderAnnouncementName: announcement.tenderAnnouncementName,
      winningBidder: announcement.winningBidder,
      winningBidAmount: announcement.winningBidAmount,
    }));

    this.logger.log(`准备推送 ${pushData.length} 条公告数据`);
    // this.logger.log(`推送数据: ${JSON.stringify(pushData)}`);
    // 发送 POST 请求
    const response = await axios.post(pushUrl, pushData, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10秒超时
    });
    // 检查响应状态
    if (response.status !== 200) {
      throw new Error(`推送失败，HTTP状态码: ${response.status}`);
    }

    this.logger.log(`推送响应: ${JSON.stringify(response.data)}`);
  }
  /**
   * 根据发布日期、状态和类型查询公告并推送到远程
   * @param publishDate 发布日期 (YYYY-MM-DD 格式)
   * @param status 状态 (可以是字符串或字符串数组)
   * @param type 类型
   * @returns 推送结果
   */
  async queryAndPushAnnouncements(
    publishDate?: string,
    type?: string,
    status?: string | string[],
  ): Promise<{
    success: boolean;
    total: number;
    pushed: number;
    message: string;
  }> {
    try {
      // 构建查询条件
      const query: any = {};

      // 添加发布日期条件
      if (publishDate) {
        const startDate = new Date(publishDate);
        const endDate = new Date(publishDate);
        endDate.setDate(endDate.getDate() + 1); // 下一天的开始时间

        query.publishDate = {
          $gte: startDate,
          $lt: endDate,
        };
      }

      // 添加状态条件
      if (status) {
        if (Array.isArray(status)) {
          // 如果是数组，使用 $in 操作符
          query.status = { $in: status };
        } else {
          // 如果是字符串，直接匹配
          query.status = status;
        }
      }

      // 添加类型条件
      if (type) {
        query.type = type;
      }

      this.logger.log(`查询条件: ${JSON.stringify(query)}`);

      // 查询数据库
      const announcements = await this.announcementModel.find(query).exec();

      this.logger.log(`查询到 ${announcements.length} 条公告数据`);

      if (announcements.length === 0) {
        return {
          success: true,
          total: 0,
          pushed: 0,
          message: '没有找到符合条件的公告数据',
        };
      }

      // 推送数据到远程
      try {
        if (type == '1') {
          await this.pushAnnouncementData(announcements);
        }
        if (type == '2') {
          await this.pushAnnouncementDataOfZhongbiao(announcements);
        }

        return {
          success: true,
          total: announcements.length,
          pushed: announcements.length,
          message: `成功推送 ${announcements.length} 条公告数据`,
        };
      } catch (pushError) {
        this.logger.error(`推送失败: ${pushError.message}`, pushError.stack);

        return {
          success: false,
          total: announcements.length,
          pushed: 0,
          message: `查询成功但推送失败: ${pushError.message}`,
        };
      }
    } catch (error) {
      this.logger.error(`查询和推送失败: ${error.message}`, error.stack);

      return {
        success: false,
        total: 0,
        pushed: 0,
        message: `操作失败: ${error.message}`,
      };
    }
  }

  /**
   * 根据日期范围查询公告并推送到远程
   * @param fromDate 开始日期 (YYYY-MM-DD 格式)
   * @param toDate 结束日期 (YYYY-MM-DD 格式)
   * @param status 状态 (可以是字符串或字符串数组)
   * @param type 类型 (可选)
   * @returns 推送结果
   */
  async queryAndPushAnnouncementsByDateRange(
    fromDate: string,
    toDate: string,
    type?: string,
    status?: string | string[],
  ): Promise<{
    success: boolean;
    total: number;
    pushed: number;
    message: string;
  }> {
    try {
      // 构建查询条件
      const query: any = {};

      // 添加日期范围条件
      const startDate = new Date(fromDate);
      const endDate = new Date(toDate);
      endDate.setDate(endDate.getDate() + 1); // 包含结束日期当天

      query.publishDate = {
        $gte: startDate,
        $lt: endDate,
      };

      // 添加状态条件
      if (status) {
        if (Array.isArray(status)) {
          // 如果是数组，使用 $in 操作符
          query.status = { $in: status };
        } else {
          // 如果是字符串，直接匹配
          query.status = status;
        }
      }

      // 添加类型条件
      if (type) {
        query.type = type;
      }

      this.logger.log(`日期范围查询条件: ${JSON.stringify(query)}`);

      // 查询数据库
      const announcements = await this.announcementModel.find(query).exec();

      this.logger.log(`查询到 ${announcements.length} 条公告数据`);

      if (announcements.length === 0) {
        return {
          success: true,
          total: 0,
          pushed: 0,
          message: '没有找到符合条件的公告数据',
        };
      }

      // 推送数据到远程
      try {
        if (type == '1') {
          await this.pushAnnouncementData(announcements);
        }
        if (type == '2') {
          await this.pushAnnouncementDataOfZhongbiao(announcements);
        }

        return {
          success: true,
          total: announcements.length,
          pushed: announcements.length,
          message: `成功推送 ${announcements.length} 条公告数据`,
        };
      } catch (pushError) {
        this.logger.error(`推送失败: ${pushError.message}`, pushError.stack);

        return {
          success: false,
          total: announcements.length,
          pushed: 0,
          message: `查询成功但推送失败: ${pushError.message}`,
        };
      }
    } catch (error) {
      this.logger.error(`查询和推送失败: ${error.message}`, error.stack);

      return {
        success: false,
        total: 0,
        pushed: 0,
        message: `操作失败: ${error.message}`,
      };
    }
  }

  // async sendAnnouncementsByCondition(
  //   areaCode: string,
  //   keywords: string[],
  //   publishDate: Date,
  // ): Promise<void> {
  //   const subscribes = await this.subscribeService.findByAreaCodeAndKeywords(
  //     areaCode,
  //     keywords,
  //   );

  //   if (subscribes.length === 0) {
  //     console.log('No subscribes found for the given criteria.');
  //     return;
  //   }

  //   const announcements = await this.announcementModel
  //     .find({ areaCode, keywords: { $in: keywords } })
  //     .exec();

  //   if (announcements.length === 0) {
  //     console.log('No announcements found for the given criteria.');
  //     return;
  //   }

  //   const transporter = nodemailer.createTransport({
  //     service: 'gmail',
  //     auth: {
  //       user: '<EMAIL>',
  //       pass: 'your-email-password',
  //     },
  //   });

  //   for (const subscribe of subscribes) {
  //     for (const announcement of announcements) {
  //       const mailOptions = {
  //         from: '<EMAIL>',
  //         to: subscribe.email,
  //         subject: announcement.title,
  //         text: announcement.content,
  //       };

  //       await transporter.sendMail(mailOptions);
  //       console.log(
  //         `Email sent to ${subscribe.email} for announcement: ${announcement.title}`,
  //       );
  //     }
  //   }
  // }
}
