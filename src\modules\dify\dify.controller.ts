import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { DifyService } from './dify.service'; // 根据你的文件结构调整路径

// 定义请求体的接口
interface ExtractRequestDto {
  text: string;
}

@Controller('/api/dify')
export class DifyController {
  constructor(private readonly difyService: DifyService) {}

  /**
   * POST 接口用于测试提取公告信息
   */
  @Post('/extract/announcement')
  async extractAnnouncement(@Body() body: ExtractRequestDto) {
    const { text } = body;

    if (!text || typeof text !== 'string') {
      throw new HttpException('缺少或无效的公告文本', HttpStatus.BAD_REQUEST);
    }

    try {
      const result = await this.difyService.extractAnnouncementInfo(text);
      return {
        message: '提取成功',
        data: result,
      };
    } catch (error) {
      // 直接抛出服务层已处理的 HttpException，或处理其他异常
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        '服务器内部错误',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
