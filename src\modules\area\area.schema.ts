import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class Area extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true })
  code: string;

  @Prop()
  provinceCode?: string;

  @Prop()
  cityCode?: string;
}
export type AreaDocument = Area & Document;
export const AreaSchema = SchemaFactory.createForClass(Area);
