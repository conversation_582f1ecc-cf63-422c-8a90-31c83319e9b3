import * as mongoose from 'mongoose';
import { Prop, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class Announcement extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: false })
  projectName: string;

  @Prop({ required: false })
  projectCode: string;

  @Prop({ required: false })
  // 招标代理 代理机构信息-代理名称
  agencyName: string;

  @Prop({ required: false })
  // 建设单位 采购人信息-名称
  constructionUnit: string;

  @Prop({ required: false })
  // 行业
  industry: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  publishDate: Date;

  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  areaCode: string;

  @Prop({ required: true })
  url: string;

  @Prop({ required: false })
  bidDeadline: Date;

  @Prop({ required: false })
  tenderEntity: string;

  @Prop({ required: false, type: [String] })
  constructionContent: string[];

  @Prop({ required: false })
  budgetAmount: string;

  @Prop({ required: false })
  budgetUnit: string;

  @Prop({ required: false })
  // 项目金额
  projectAmount: string;

  @Prop({ required: false })
  // 项目金额单位
  projectUnit: string;

  @Prop({ required: false })
  // 附件名称
  attachmentName: string;

  @Prop({ required: false })
  // 附件链接
  attachmentUrl: string;

  @Prop({ required: false })
  // 状态
  status: string;

  @Prop({ required: false })
  // 来源
  source: string;

  @Prop({ required: false })
  // 省份名称
  provinceName: string;

  @Prop({ required: false })
  // 招标公告链接
  tenderAnnouncementLink: string;

  @Prop({ required: false })
  // 招标公告名称
  tenderAnnouncementName: string;

  @Prop({ required: false })
  // 中标单位
  winningBidder: string;

  @Prop({ required: false })
  // 中标金额
  winningBidAmount: string;
}

export type AnnouncementDocument = Announcement & Document;
export const AnnouncementSchema = SchemaFactory.createForClass(Announcement);
