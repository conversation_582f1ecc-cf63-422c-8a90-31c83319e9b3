import * as mongoose from 'mongoose';
import { Prop, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class Announcement extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: false })
  projectName: string;

  @Prop({ required: false })
  projectCode: string;

  @Prop({ required: false })
  agencyName: string;

  @Prop({ required: false })
  constructionUnit: string;

  @Prop({ required: false })
  industry: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  publishDate: Date;

  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  areaCode: string;

  @Prop({ required: true })
  url: string;

  @Prop({ required: false })
  bidDeadline: Date;

  @Prop({ required: false })
  tenderEntity: string;

  @Prop({ required: false, type: [String] })
  constructionContent: string[];

  @Prop({ required: false })
  budgetAmount: string;

  @Prop({ required: false })
  budgetUnit: string;

  @Prop({ required: false })
  projectAmount: string;

  @Prop({ required: false })
  projectUnit: string;

  @Prop({ required: false })
  attachmentName: string;

  @Prop({ required: false })
  attachmentUrl: string;

  @Prop({ required: false })
  status: string;

  @Prop({ required: false })
  source: string;
}

export type AnnouncementDocument = Announcement & Document;
export const AnnouncementSchema = SchemaFactory.createForClass(Announcement);
