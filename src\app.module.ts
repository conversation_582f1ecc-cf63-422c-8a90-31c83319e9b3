import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './modules/user/user.module';
import { AuthModule } from './modules/auth/auth.module';
import { MongooseModule, MongooseModuleOptions } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AnnouncementModule } from './modules/announcement/announcement.module';
import { AreaModule } from './modules/area/area.module';
import { WechatModule } from './modules/wechat/wechat.module';
import { SubscribeModule } from './modules/subscribe/subscribe.module';
import { ScheduleModule } from './modules/schedule/schedule.module';
import { ScheduleModule as NestScheduleModule } from '@nestjs/schedule';
import { SpiderModule } from './modules/spider/spider.module';
import { DifyModule } from './modules/dify/dify.module';
import { BidderModule } from './modules/bidder/bidder.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'], // 优先读取 .env.local，然后读取 .env
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const mongooseOptions: MongooseModuleOptions = {
          uri: configService.get('MONGO_URI'),
          dbName: configService.get('MONGO_DB_NAME'),
          user: configService.get('MONGO_USER'),
          pass: configService.get('MONGO_PASS'),
          authSource: configService.get('MONGO_AUTH_SOURCE'),
        };
        return mongooseOptions;
      },
      inject: [ConfigService],
    }),
    UserModule,
    AuthModule,
    AnnouncementModule,
    AreaModule,
    WechatModule,
    SubscribeModule,
    NestScheduleModule.forRoot(),
    ScheduleModule,
    SpiderModule,
    DifyModule,
    BidderModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
