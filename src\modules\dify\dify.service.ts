import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import axios, { AxiosError } from 'axios';

@Injectable()
export class DifyService {
  private readonly apiUrl = 'http://10.10.7.240:82/v1/workflows/run';

  /**
   * 调用外部接口
   * @param inputs - 请求体中的 inputs 数据
   * @param user - 请求体中的 user 数据
   * @returns 返回外部接口的响应数据
   */
  async extractAnnouncementInfo(text: string) {
    try {
      const apiKey = 'app-KIGAFs7hnWWRPPTWZ5KSdS97';
      const response = await axios.post(
        this.apiUrl,
        {
          inputs: {
            announcement_text: text,
          },
          response_mode: 'blocking',
          user: 'abc-123',
        },
        {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      // 返回外部接口的响应数据
      return response.data;
    } catch (error) {
      // 处理 Axios 错误
      if (error instanceof AxiosError) {
        const errorMessage =
          error.response?.data?.message || '调用Dify工作流提取公告信息失败';
        throw new HttpException(
          errorMessage,
          error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      // 处理其他未知错误
      throw new HttpException(
        '提取公告信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
