import axios from 'axios';
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import { AnnouncementService } from '../announcement/announcement.service';

@Injectable()
export class SpiderService implements OnModuleInit {
  private readonly logger = new Logger('【爬虫服务】：');
  constructor(private readonly announcementService: AnnouncementService) {}

  async onModuleInit() {
    // try {
    //   this.logger.log('模块初始化 - 开始查询并推送公告数据');
    //   // 查询2025-07-10的指定状态公告并推送
    //   const result = await this.announcementService.queryAndPushAnnouncements(
    //     '2025-07-10',
    //     ['中标（成交）结果公告'],
    //   );
    //   this.logger.log(`查询推送结果: ${JSON.stringify(result)}`);
    // } catch (error) {
    //   this.logger.error(
    //     `模块初始化查询推送失败: ${error.message}`,
    //     error.stack,
    //   );
    // }
    // this.crawlZhejiangZhengcaiAnnouncements();//useTo:1 招标公告 useTo:2 中标结果
    // this.crawlZhejiangZhengcaiAnnouncementsOfZhongbiao();
    // this.crawlSiChuanPublicAnnouncements();
    // this.crawlHuNanBiddingAnnouncements();
    // this.crawlGuangDongPublicAnnouncements();
    //this.crawlAnHuiAnnouncements();
    // this.crawlHuBeiGongGongZiYuan();
    // this.crawlChongQinggonggongziyuan();
    // this.crawlZhejiangPublicWinAnnouncements();
    // this.crawlZhejiangPublicWinAnnouncementsOfZhongbiao();
    // this.crawlZhejiangZhengcaiWinAnnouncements()
    // this.JiangXiAnnouncements();
    // this.crawlZhejiangZhengcaiWinAnnouncements();
    // this.crawJiangXiAnnouncements();
    // this.crawlFuJianBiddingAnnouncements();
    // this.crawlNeiMengPublicAnnouncements();
    // this.crawlXinJiangPublicAnnouncements();
    // this.crawlJiangSuPublicAnnouncements();
    // this.crawlSiChuanPublicWinAnnouncements();
    // this.crawlChongQingPublicWinAnnouncements();
  }

  // 将 ReadableStream 转换为字符串
  async streamToString(stream) {
    const chunks = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }
    return Buffer.concat(chunks).toString('utf8');
  }

  sleep(milliseconds) {
    return new Promise((resolve) => setTimeout(resolve, milliseconds));
  }

  getTodayString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  saveAnnouncements = async (data) => {
    for (const item of data) {
      try {
        await this.announcementService.create(item);
      } catch (error) {
        this.logger.error(`保存公告失败： ${item.title}:`, error);
      }
    }
    return true;
  };

  // ******
  // 地区：浙江
  // 网站名称：浙江公共资源交易中心
  // 网站链接：https://ggzy.zj.gov.cn/jyxxgk/list.html
  // 爬取内容：招标公告
  // 爬取时间：每天23:30
  // ******
  @Cron('30 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlZhejiangPublicAnnouncements() {
    this.logger.log('爬虫开始-浙江公共资源交易中心');
    const data: any[] = [];
    let isToday = true; // 标志变量，表示是否还在抓取当天的公告
    // 获取今天的日期
    const today = this.getTodayString();

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.goto('https://ggzy.zj.gov.cn/jyxxgk/list.html');

    try {
      // 等待招标公告按钮出现并点击
      await page.waitForSelector('xpath=//*[@id="leftmenu"]/li[2]/a', {
        visible: true,
      });
      const element = await page.locator('xpath=//*[@id="leftmenu"]/li[2]/a');
      if (element) {
        // 点击按钮并等待页面加载
        await Promise.all([
          element.click(),
          page.waitForResponse((response) =>
            response
              .url()
              .includes(
                '/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew',
              ),
          ), // 等待特定的 API 请求
        ]);
      } else {
        console.log('Element not found');
        return;
      }

      // 循环抓取所有页面的公告
      while (isToday) {
        // 重新获取页面内容
        const html = await page.content();
        const $ = cheerio.load(html);

        // 解析当前页面的公告列表
        $('.ewb-public-item.clearfix').each((index, element) => {
          const input = $(element).find('a').text().trim();
          const splitArr = input.split(']');
          const area = splitArr[0].replace('[', '').trim(); // 地区
          const title = splitArr[1] + ']'; // 项目名
          const url = $(element).find('a').attr('href');
          const publishDate = $(element).find('.ewb-date').text().trim(); // 确保选择器正确

          // 如果日期不是当天，停止抓取
          if (publishDate !== today) {
            isToday = false;
            return;
          }

          // 只抓取当天的公告
          if (title && url) {
            data.push({
              title,
              url: `https://ggzy.zj.gov.cn${url}`, // 完整链接
              publishDate,
              area,
              type: '1',
            });
          }
        });

        // 如果已经抓取完当天的所有公告，停止循环
        if (!isToday) break;

        // 检查是否存在下一页按钮
        const nextPageButton = await page.locator('xpath=//a[text()="下一页"]');
        if (nextPageButton) {
          // 点击下一页按钮并等待页面加载
          await Promise.all([nextPageButton.click()]);
        } else {
          // 如果没有下一页按钮，停止循环
          isToday = false;
        }
      }
      // 遍历符合条件的公告，点击并抓取详情页内容
      for (const item of data) {
        try {
          // 点击公告链接并抓取详情页内容
          const res = await axios.get(item.url);
          const $ = cheerio.load(res.data);

          // 解析详情页内容
          const content = $('table')
            .text()
            .trim()
            .replace(/\n\s*\n+/g, '\n');

          // 更新数据对象
          item.content = content;

          this.logger.log(`成功爬取公告：${item.title}`);
        } catch (error) {
          this.logger.error(`爬取公告失败： ${item.title}:`, error);
        }
      }
    } catch (error) {
      console.error('爬取公告失败：', error);
    } finally {
      // 关闭浏览器
      await browser.close();
    }

    for (const item of data) {
      try {
        await this.announcementService.create(item);
      } catch (error) {
        this.logger.error(`保存公告失败： ${item.title}:`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：浙江
  // 网站名称：浙江政采网
  // 网站链接：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement
  // 爬取内容：采购公告(采购意向，意见征询，采购项目公告，更正公告,非政府采购公告)
  // 爬取时间：每天23:31
  // ******
  @Cron('31 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlZhejiangZhengcaiAnnouncements() {
    this.logger.log('爬虫开始-浙江政采网');

    const getZhejiangZhengcaiArea = (str: string) => {
      if (str === '浙江') {
        return '浙江省';
      }
      if (str.length > 6) {
        return str.slice(6);
      }
      return str;
    };

    const data: any[] = [];
    let isToday = true; // 标志变量，表示是否还在抓取当天的公告
    // 获取今天的日期
    const today = this.getTodayString();

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.goto(
      'https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement',
    );

    try {
      const statusDic = {
        1: '采购意向',
        2: '意见征询',
        3: '采购项目公告',
        4: '更正公告',
        5: '非政府采购公告',
      };
      for (let i = 1; i <= 5; i++) {
        isToday = true;
        // 等待左侧树形菜单加载完成
        let element = await page.locator(
          `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[1]/div/div/div[2]/div[${i}]/div[1]/span[2]`,
        );
        if (i == 5) {
          element = await page.locator(
            `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[1]/div/div/div[4]/div[1]/div`,
          );
        }

        if (element) {
          // 点击按钮并等待页面加载
          await element.click();
          if (i == 5) {
            await page
              .locator(
                `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[2]/div[1]/div/div/form/div[1]/div/div/input`,
              )
              .fill('招标公告');
            await page
              .locator(
                `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[2]/div[1]/div/div/form/div[9]/div/button[1]`,
              )
              .click();
          }
          await page.waitForResponse((response) =>
            response.url().includes('/portal/category'),
          );
        } else {
          console.log('Element not found');
          return;
        }

        const dataOfTab = [];
        // 循环抓取列表所有页面的公告
        while (isToday) {
          // 重新获取页面内容
          const html = await page.content();
          const $ = cheerio.load(html);

          // 解析当前页面的公告列表
          $('div > div.search-list > ul > li').each((index, element) => {
            const title = $(element).find('a').text().trim();
            const area = // 项目名
              getZhejiangZhengcaiArea(
                $(element)
                  .find('.title-head')
                  .text()
                  .trim()
                  .split('·')?.[0]
                  .split('\n')?.[0],
              );
            const url = $(element).find('a').attr('href');
            const publishDate = $(element).find('.publish-time').text().trim(); // 确保选择器正确
            // 如果日期不是当天，停止抓取
            if (publishDate !== today) {
              isToday = false;
              return;
            }

            // 只抓取当天的公告
            if (title && url) {
              dataOfTab.push({
                title,
                url: `https://zfcg.czt.zj.gov.cn${url}`, // 完整链接
                publishDate,
                area: '浙江省',
                type: '1', //1招标公告,2中标内容
                status: statusDic[i],
                source: '浙江政府采购网',
                province_name: '浙江省',
              });
            }
          });

          // 如果已经抓取完当天的所有公告，停止循环
          if (!isToday) break;

          // 检查是否存在下一页按钮
          const nextPageButton = await page.locator(
            'div > div.page > div > button.btn-next',
          );
          if (nextPageButton) {
            // 点击下一页按钮并等待页面加载
            await Promise.all([nextPageButton.click()]);
          } else {
            // 如果没有下一页按钮，停止循环
            isToday = false;
          }
        }
        // 遍历符合条件的公告，点击并抓取详情页内容
        for (const item of dataOfTab) {
          try {
            // 点击公告链接并抓取详情页内容
            const res = await axios.get(item.url.replace('site', 'portal'));
            // 更新数据对象
            item.content = res.data?.result?.data?.content;

            this.logger.log(`成功爬取公告：${item.title}`);
          } catch (error) {
            this.logger.error(`爬取公告失败： ${item.title}:`, error);
          }
        }
        data.push(...dataOfTab);
      }
    } catch (error) {
      console.error('爬取公告失败：', error);
    } finally {
      // 关闭浏览器
      await browser.close();
    }
    const pushData = [];
    for (const item of data) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue; // 如果公告已存在，则跳过保存
        }

        // 如果公告不存在，则创建新公告
        const savedAnnouncement = await this.announcementService.create(item);
        if (savedAnnouncement) {
          pushData.push(savedAnnouncement);
        }
        this.logger.log(`公告保存成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }

    // 推送数据到外部接口
    try {
      await this.announcementService.pushAnnouncementData(
        pushData,
        'http://10.13.4.66:8054/rest/WBusinessCollect/saveInfo',
      );
      this.logger.log(`公告数据推送成功`);
    } catch (error) {
      this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
      // 注意：这里不抛出错误，避免影响公告创建流程
    }
    return true;
  }

  // ******
  // 地区：浙江
  // 网站名称：浙江政采网
  // 网站链接：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement
  // 爬取内容：采购公告(采购结果公告-中标（成交结果公告），非政府采购公告（'结果'）)
  // 爬取时间：每天23:25
  // ******
  @Cron('25 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlZhejiangZhengcaiAnnouncementsOfZhongbiao() {
    this.logger.log('爬虫开始-浙江政采网');

    const getZhejiangZhengcaiArea = (str: string) => {
      if (str === '浙江') {
        return '浙江省';
      }
      if (str.length > 6) {
        return str.slice(6);
      }
      return str;
    };

    const data: any[] = [];
    let isToday = true; // 标志变量，表示是否还在抓取当天的公告
    // 获取今天的日期
    const today = this.getTodayString();

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.goto(
      'https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement',
    );

    try {
      const statusDic = {
        0: '中标（成交）结果公告',
        1: '非政府采购公告(结果)',
      };
      for (let i = 0; i <= 1; i++) {
        isToday = true;
        // 等待左侧树形菜单加载完成
        let element;
        if (i == 0) {
          element = await page.locator(
            'xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[1]/div/div/div[2]/div[5]/div[1]/span[1]',
          );
        }
        if (i == 1) {
          element = await page.locator(
            `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[1]/div/div/div[4]/div[1]/div`,
          );
        }

        if (element) {
          // 点击按钮并等待页面加载
          await element.click();
          if (i == 0) {
            const tenderAnnouncement = await page.locator(
              'xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[1]/div/div/div[2]/div[5]/div[2]/div[1]/div/span[2]',
            );
            if (tenderAnnouncement) {
              await tenderAnnouncement.click();
            }
          }
          if (i == 1) {
            await page
              .locator(
                `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[2]/div[1]/div/div/form/div[1]/div/div/input`,
              )
              .fill('结果');
            await page
              .locator(
                `xpath=//*[@id="floor-1669024014247095"]/div/div[2]/div[2]/div[1]/div/div/form/div[9]/div/button[1]`,
              )
              .click();
          }
          await page.waitForResponse((response) =>
            response.url().includes('/portal/category'),
          );
        } else {
          console.log('Element not found');
          return;
        }

        const dataOfTab = [];
        // 循环抓取列表所有页面的公告
        while (isToday) {
          // 重新获取页面内容
          const html = await page.content();
          const $ = cheerio.load(html);

          // 解析当前页面的公告列表
          $('div > div.search-list > ul > li').each((index, element) => {
            const title = $(element).find('a').text().trim();
            const area = // 项目名
              getZhejiangZhengcaiArea(
                $(element)
                  .find('.title-head')
                  .text()
                  .trim()
                  .split('·')?.[0]
                  .split('\n')?.[0],
              );
            const url = $(element).find('a').attr('href');
            const publishDate = $(element).find('.publish-time').text().trim(); // 确保选择器正确
            // 如果日期不是当天，停止抓取
            if (publishDate !== today) {
              isToday = false;
              return;
            }

            // 只抓取当天的公告
            if (title && url) {
              dataOfTab.push({
                title,
                url: `https://zfcg.czt.zj.gov.cn${url}`, // 完整链接
                publishDate,
                area: '浙江省',
                type: '2', //中标信息
                status: statusDic[i],
                source: '浙江政府采购网',
                province_name: '浙江省',
              });
            }
          });

          // 如果已经抓取完当天的所有公告，停止循环
          if (!isToday) break;

          // 检查是否存在下一页按钮
          const nextPageButton = await page.locator(
            'div > div.page > div > button.btn-next',
          );

          if (nextPageButton) {
            // 点击下一页按钮并等待页面加载
            await Promise.all([nextPageButton.click()]);
          } else {
            // 如果没有下一页按钮，停止循环
            isToday = false;
          }
        }
        // 遍历符合条件的公告，点击并抓取详情页内容
        for (const item of dataOfTab) {
          try {
            // 点击公告链接并抓取详情页内容
            const res = await axios.get(item.url.replace('site', 'portal'));
            // 更新数据对象
            item.content = res.data?.result?.data?.content;
            // 获取采购公告地址
            const caigouItem = res.data?.result?.data?.newLinkNodeList?.find(
              (item) => item.nodeName === '采购公告',
            );
            if (caigouItem) {
              const finalUrl =
                'https://zfcg.czt.zj.gov.cn/site/detail?parentId=600007&articleId=' +
                caigouItem.relatedArticles?.[0]?.articleId;
              item.tender_announcement_name =
                caigouItem.relatedArticles?.[0]?.title;
              item.tender_announcement_link = finalUrl;
            }

            this.logger.log(`成功爬取公告：${item.title}`);
          } catch (error) {
            this.logger.error(`爬取公告失败： ${item.title}:`, error);
          }
        }
        data.push(...dataOfTab);
      }
    } catch (error) {
      console.error('爬取公告失败：', error);
    } finally {
      // 关闭浏览器
      await browser.close();
    }
    const pushData = [];
    for (const item of data) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue; // 如果公告已存在，则跳过保存
        }

        // 如果公告不存在，则创建新公告
        const savedAnnouncement = await this.announcementService.create(item);
        if (savedAnnouncement) {
          pushData.push(savedAnnouncement);
        }
        this.logger.log(`公告保存成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }

    // 推送数据到外部接口
    try {
      await this.announcementService.pushAnnouncementDataOfZhongbiao(
        pushData,
        'http://10.13.4.66:8054/rest/WBidAnnouncementController/addBidAnnouncement',
      );
      this.logger.log(`公告数据推送成功`);
    } catch (error) {
      this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
      // 注意：这里不抛出错误，避免影响公告创建流程
    }
    return true;
  }
  // ******
  // 地区：四川
  // 网站名称：四川省公共资源交易信息网
  // 网站链接：https://ggzyjy.sc.gov.cn/
  // 爬取内容：采购公告
  // 爬取时间：每天23:32
  // ******
  @Cron('32 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlSiChuanPublicAnnouncements() {
    this.logger.log('爬虫开始-四川省公共资源交易信息网');
    // 获取今天的日期
    const today = this.getTodayString();
    // 通过列表分页接口直接获取公告列表

    const getList = async (page) => {
      return await axios.post(
        'https://ggzyjy.sc.gov.cn/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: (page - 1) * 12,
          rn: 12,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: '',
          cnum: '',
          sort: '{"webdate":"0"}',
          ssort: '',
          cl: 10000,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              equal: '002001001',
              notEqual: null,
              equalList: null,
              notEqualList: null,
              isLike: true,
              likeType: 2,
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`, // 今天的日期,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: null,
          accuracy: '',
          noParticiple: '1',
          searchRange: null,
          noWd: true,
        },
      );
    };

    const processZhuanzai = (zhuanzai) => {
      // 如果 zhuanzai 为空，返回空字符串
      if (!zhuanzai) return '';

      // 先按 "公共资源" 分割，取 "公共资源" 前面的内容
      const area_pub = zhuanzai.split('公共资源');
      let area = area_pub[0].trim(); // 取"公共资源"前面的内容

      // if有“政务服务",取 "政务服务" 前面的内容
      if (area.includes('政务服务')) {
        area = area.split('政务服务')[0].trim();
      }

      return area;
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      let page = 1;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(page);

        if (
          data &&
          data.data?.result?.records &&
          data.data?.result?.records.length > 0
        ) {
          const dataArray = [];
          // 遍历每个公告，提取并处理 zhuanzai 字段
          for (const item of data.data.result.records) {
            try {
              const detailRes = await axios.get(
                `https://ggzyjy.sc.gov.cn/${item.linkurl}`,
              );
              dataArray.push({
                title: item.title,
                content: detailRes.data,
                publishDate: item.webdate.split(' ')[0],
                type: '1',
                area: processZhuanzai(item.zhuanzai),
                url: `https://ggzyjy.sc.gov.cn/${item.linkurl}`,
              });
              this.logger.log(`成功爬取公告：${item.title}`);
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${item.title}:`, error);
            }
          }

          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          page++; // 增加页码
        } else {
          hasMoreData = false; // 如果没有数据，停止循环
        }
      } //

      return allData;
    };

    const announcements = await getAllAnnouncements();
    const res = await this.saveAnnouncements(announcements);
    return res;
  }

  // ******
  // 地区：湖南
  // 网站名称：湖南省招标投标监管网
  // 网站链接：http://bidding.fgw.hunan.gov.cn/bidding/notice?categoryId=88
  // 爬取内容：招标公告
  // 爬取时间：每天23:10
  // ******
  @Cron('10 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlHuNanBiddingAnnouncements() {
    const getList = async (page) => {
      const url = `http://bidding.fgw.hunan.gov.cn/ztb/api/getBiddingList?limit=10&page=${page}&categoryId=88`;
      return await axios.get(url);
    };

    const getContent = async (item, url) => {
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      let content = '';
      try {
        // 点击公告链接并抓取详情页内容
        const newPage = await browser.newPage();
        await newPage.goto(url);
        await newPage.waitForSelector('.main-container', {
          visible: true,
          timeout: 120000,
        });
        const html = await newPage.content();
        const $ = cheerio.load(html);

        if (item.dataBatchType === '01') {
          $('.main-container')
            .find('.el-form.tools.hidden-sm-and-down')
            .remove();
          content = $('.main-container').html();
        } else if (item.dataBatchType === '00') {
          $('.main-container')
            .find('.el-form.tools.hidden-sm-and-down')
            .remove();
          const iframe = $('.main-container').find('iframe');
          iframe.attr(
            'src',
            `http://bidding.fgw.hunan.gov.cn${iframe.attr('src')}`,
          );
          content = $('.main-container').html();
        }

        this.logger.log(`成功爬取公告：${item.noticeTitle}`);
      } catch (error) {
        this.logger.error(`爬取公告失败： ${item.noticeTitle}:`, error);
      } finally {
        await browser.close();
      }
      return content;
    };

    this.logger.log('爬虫开始-湖南省招标投标监管网-招标公告');
    // 获取今天的日期
    const today = this.getTodayString();
    let allData = [];
    let current = 1;
    let isToday = true; // 标志变量，表示是否还在抓取当天的公告

    try {
      // 循环抓取所有页面的公告
      while (isToday) {
        const res = await getList(current);
        if (res && res?.data && res?.data?.page?.list) {
          const dataArray = [];
          for (const item of res?.data?.page?.list) {
            if (item.createTime?.indexOf(today) > -1) {
              const url = `http://bidding.fgw.hunan.gov.cn/bidding/notice/${item.id}?isdetail=1`;
              const content = await getContent(item, url);
              dataArray.push({
                title: item.noticeTitle,
                content,
                publishDate: item.createTime.split(' ')[0],
                type: '1',
                area: item.areaName === '省本级' ? '湖南省' : item.areaName,
                url,
              });
            } else {
              isToday = false;
            }
            await this.sleep(3000);
          }
          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          current++; // 增加页码
        } else {
          isToday = false;
        }
      }
    } catch (error) {
      console.error('爬取公告失败：', error);
    }

    for (const item of allData) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }
  // 地区：广东
  // 网站名称：广东省公共资源交易平台
  // 网站链接：https://ygp.gdzwfw.gov.cn/#/44/jygg
  // 爬取内容：招标公告
  // 爬取时间：每天23:14
  // ******
  @Cron('14 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlGuangDongPublicAnnouncements() {
    this.logger.log('爬虫开始-广东省公共资源交易平台');
    // 获取今天的日期
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');

    const getList = async (page) => {
      return await axios.post(
        'https://ygp.gdzwfw.gov.cn/ggzy-portal/search/v2/items',
        {
          type: 'trading-type',
          openConvert: false,
          keyword: '',
          siteCode: '44',
          secondType: 'A',
          tradingProcess: '2C14,2C81,3C14,3C81,503,517',
          thirdType: '[]',
          projectType: '',
          publishStartTime: `${year}${month}${day}000000`,
          publishEndTime: `${year}${month}${day}235959`,
          pageNo: page,
          pageSize: 10,
        },
      );
    };
    const getNodeList = async (data) => {
      return await axios.get(
        `https://ygp.gdzwfw.gov.cn/ggzy-portal/center/apis/trading-notice/new/nodeList?siteCode=${data.regionCode}&tradingType=A&bizCode=${data.tradingProcess}&projectCode=${data.projectCode}&classify=${data.projectType}`,
      );
    };
    const getDetail = async (data) => {
      return await axios.get(
        `https://ygp.gdzwfw.gov.cn/ggzy-portal/center/apis/trading-notice/new/detail?nodeId=${data.nodeId}&version=v3&tradingType=A&noticeId=${data.noticeId}&bizCode=${data.tradingProcess}&projectCode=${data.projectCode}&siteCode=${data.regionCode}`,
      );
    };
    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      let page = 1;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(page);
        if (data && data.data?.data && data.data?.data.pageData.length > 0) {
          const dataArray = [];
          // 遍历每个公告，提取并处理 zhuanzai 字段
          for (const item of data.data.data.pageData) {
            try {
              const publishDate = `${item.publishDate.substring(
                0,
                4,
              )}-${item.publishDate.substring(
                4,
                6,
              )}-${item.publishDate.substring(6, 8)}`;
              const nodeData = await getNodeList(item);
              const nodeId = nodeData.data?.data?.[0]?.nodeId;
              const url = `https://ygp.gdzwfw.gov.cn/#/44/new/jygg/v3/A?noticeId=${
                item.noticeId
              }&projectCode=${item.projectCode}&bizCode=${
                item.tradingProcess
              }&siteCode=${item.regionCode}&publishDate=${
                item.publishDate
              }&source=${encodeURIComponent(
                item.pubServicePlat,
              )}&titleDetails=${encodeURIComponent(
                item.noticeSecondTypeDesc,
              )}&classify=${item.projectType}&nodeId=${nodeId}`;
              // 获取详情页信息
              const detailData = await getDetail({ nodeId, ...item });
              let content = '';
              for (const el of detailData.data?.data
                ?.tradingNoticeColumnModelList) {
                // 公告信息
                if (el.multiKeyValueTableList?.[0]?.length > 0) {
                  const html = el.multiKeyValueTableList?.[0]
                    .map(
                      (item) =>
                        `<tr><td colspan="1">${item.aliasName}</td><td colspan="3">${item.value}</td></tr>`,
                    )
                    .join('');
                  content += `<h2>${el.name}</h2><table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;"><tbody>${html}</tbody></table>`;
                }
                // 公告内容
                if (el.richtext) {
                  content += `<hr><h2>${el.name}</h2>${el.richtext}`;
                }
                // 相关附件
                if (el.noticeFileBOList?.length > 0) {
                  const html = el.noticeFileBOList
                    .map(
                      (file, index) =>
                        `<tr><td style="width: 180px;">附件名称 ${
                          index + 1
                        }</td><td>${file.fileName}</td></tr>`,
                    )
                    .join('');
                  content += `<h2>${el.name}</h2><table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;"><tbody>${html}</tbody></table>`;
                }
              }
              dataArray.push({
                title: item.noticeTitle,
                content,
                publishDate,
                type: '1',
                area: item.siteName?.replace('市辖区', ''),
                url,
              });
              await this.sleep(10000);
              this.logger.log(`获取公告内容成功： ${item.noticeTitle}:`);
            } catch (error) {
              this.logger.error(
                `获取公告内容失败： ${item.noticeTitle}:`,
                error,
              );
            }
          }

          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          if (page < data.data?.data.pageTotal) {
            page++; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果没有数据，停止循环
        }
      } //
      this.logger.log('爬虫完成');
      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：重庆
  // 网站名称：重庆市公共资源交易网
  // 网站链接：https://www.cqggzy.com/jyxx/transaction_detail.html
  // 爬取内容：采购公告
  // 爬取时间：每天23:10
  // ******
  @Cron('10 23 * * *', { timeZone: 'Asia/Shanghai' }) // 每天15点执行
  async crawlChongQinggonggongziyuan() {
    this.logger.log('爬虫开始-重庆市公共资源交易网');
    const data: any[] = [];

    const isToday = true; // 标志变量，表示是否还在抓取当天的公告
    // 获取今天的日期
    const today = this.getTodayString();
    // 通过列表分页接口直接获取公告列表

    const getContent = async (item, url) => {
      let content = '';
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      try {
        // 点击公告链接并抓取详情页内容
        const newPage = await browser.newPage();
        await newPage.goto(url);
        await newPage.waitForSelector('.tabview-title', {
          visible: true,
          timeout: 30000,
        });
        await Promise.all([
          newPage.click('.tabview-title .clearfix > div'),
          newPage.waitForSelector('.epoint-article-content', {
            visible: true,
            timeout: 30000,
          }),
        ]);
        const html = await newPage.content();
        const $ = cheerio.load(html);

        content = $('.epoint-article-content').html();

        this.logger.log(`成功爬取公告：${item.title}`);
      } catch (error) {
        this.logger.error(`爬取公告失败： ${url}:`, error);
      } finally {
        await browser.close();
      }
      return content;
    };

    const getList = async (page) => {
      return await axios.post(
        'https://www.cqggzy.com/interface/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: (page - 1) * 20,
          rn: 20,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: '',
          cnum: '001',
          sort: '{"istop":"0","ordernum":"0","webdate":"0","rowid":"0"}',
          ssort: '',
          cl: 10000,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              equal: '014001001',
              notEqual: null,
              equalList: null,
              notEqualList: [
                '014001018',
                '004002005',
                '014001015',
                '014005014',
                '014008011',
              ],
              isLike: true,
              likeType: 2,
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: [],
          accuracy: '',
          noParticiple: '1',
          searchRange: null,
          noWd: true,
        },
      );
    };

    const processarea = (infoc) => {
      // 如果 infoc 为空，返回空字符串
      if (!infoc) return '';
      // 判断是否为市级
      const isCityLevel = infoc.includes('市级');
      // 如果是市级，area 为 "重庆市"，否则为 infoc 的内容
      let area = isCityLevel ? '重庆市' : infoc.trim();
      if (area === '酉阳县') {
        area = '酉阳土家族苗族自治县';
      } else if (area === '石柱县') {
        area = '石柱土家族自治县';
      }
      return area;
    };

    const getAllAnnouncements = async () => {
      let page = 1;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        try {
          const response = await getList(page);
          const records = response.data?.result?.records || [];

          if (records?.length > 0) {
            const dataArray1 = [];
            for (const announcement of records) {
              const { infoid, infodate } = announcement;
              const date = infodate.split(' ')[0].replace(/-/g, '');
              const catanum = announcement.categorynum;
              const url1 = `https://www.cqggzy.com/xxhz/014001/014001001/${catanum}/${date}/${infoid}.html`;
              try {
                const detailresponse = await getContent(announcement, url1);
                dataArray1.push({
                  title: announcement.title,
                  content: detailresponse,
                  publishDate: announcement.webdate.split(' ')[0],
                  type: '1',
                  area: processarea(announcement.infoc),
                  url: url1,
                });
              } catch (error) {
                this.logger.error(
                  `获取公告内容失败： ${announcement.title}:`,
                  error,
                );
              }
              await this.sleep(5000);
            }
            allData = [...allData, ...dataArray1]; // 合并数据
            page++; // 下一页
            if (allData.length >= response.data?.result?.totalcount) {
              hasMoreData = false;
            }
          } else {
            hasMoreData = false; // 如果当前页没有数据，停止循环
          }
        } catch (error) {
          console.error('Error fetching data:', error.message);
          break; // 请求出错，退出循环
        }
      }

      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：安徽
  // 网站名称：安徽省电子招投标交易平台
  // 网站链接：https://ggzy.ah.gov.cn/jsgc/list
  // 爬取内容：采购公告
  // 爬取时间：每天23:32
  // ******

  @Cron('31 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlAnHuiAnnouncements() {
    this.logger.log('爬虫开始-安徽公共资源交易中心网');

    // 修改后的数据提取逻辑
    const data = [];
    const today = this.getTodayString();

    const processarea = (infoc) => {
      if (!infoc) return '';
      const area1 = infoc.split('-')[0].trim();
      const area = area1.split('【')[1]?.trim() || area1; // 使用可选链式调用避免错误
      return area;
    };

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const tenderProjectTypes = [1, 2, 3, 4]; // 定义需要访问的tenderProjectType

    for (const type of tenderProjectTypes) {
      let isToday = true; // 每次处理新的type时重新初始化isToday
      let currentPage = 1; // 初始化当前页码
      const page = await browser.newPage();
      try {
        await page.goto(
          `https://ggzy.ah.gov.cn/jsgc/list?tenderProjectType=${type}`,
          {
            timeout: 60000, // 增加超时时间到60秒
            waitUntil: 'networkidle0',
          },
        );

        while (isToday) {
          const html = await page.content();
          const $ = cheerio.load(html);

          $('.list ul .list-item').each((index, element) => {
            const titleElement = $(element).find('.title');
            const areaElement = $(element).find('.area');
            const dateElement = $(element).find('.date');
            const urlElement = $(element).find('a');

            const url = urlElement.attr('href');
            const title =
              titleElement.attr('title') || titleElement.text().trim();
            const area = areaElement.text().trim();
            const publishDate = dateElement.text().trim();

            // 日期检查
            if (publishDate !== today) {
              isToday = false;
              return false; // 退出当前.each循环
            }

            if (title && url) {
              data.push({
                title,
                url: `https://ggzy.ah.gov.cn${url}`, // 注意保持URL拼接逻辑正确
                publishDate,
                area: processarea(area),
                type: type.toString(), // 确保使用当前循环中的type值
              });
            }
          });

          if (!isToday) break;

          // 根据当前页码选择正确的XPath表达式
          let nextPageLink;
          if (currentPage < 4) {
            nextPageLink = await page.$(
              'xpath=/html/body/div[1]/div[3]/div[3]/div/div[2]/div[4]/div/a[8]',
            );
          } else {
            nextPageLink = await page.$(
              'xpath=/html/body/div[1]/div[3]/div[3]/div/div[2]/div[4]/div/a[9]',
            );
          }

          if (nextPageLink) {
            try {
              await Promise.all([
                nextPageLink.click(),
                page.waitForNavigation({
                  waitUntil: 'networkidle0',
                  timeout: 60000,
                }),
              ]);
              currentPage++; // 增加当前页码
            } catch (error) {
              this.logger.error('点击下一页链接时出错:', error);
              isToday = false;
            }
          } else {
            isToday = false;
          }
        }
      } catch (error) {
        this.logger.error(`处理tenderProjectType=${type}时出错：`, error);
      } finally {
        await page.close(); // 关闭当前页面
      }
    }

    await browser.close();

    // 遍历符合条件的公告，抓取详情页内容
    for (const item of data) {
      try {
        const res = await axios.get(item.url);
        const $ = cheerio.load(res.data);
        const content = $('body').html(); // 修改此处获取整个body内容或其他指定部分
        item.content = content;
        this.logger.log(`成功爬取公告：${item.title}`);
      } catch (error) {
        this.logger.error(`爬取公告失败： ${item.title}:`, error);
      }
    }

    // 保存数据
    for (const item of data) {
      try {
        await this.announcementService.create(item);
      } catch (error) {
        this.logger.error(`保存公告失败： ${item.title}:`, error);
      }
    }

    return true;
  }

  // ******
  // 地区：湖北
  // 网站名称：湖北省电子招投标交易平台
  // 网站链接：https://www.hbggzyfwpt.cn/jyxx/jsgcXmxx?businessTypeValue=0&messageTypeValue=2&publishTime=4&startTime=2024-08-16&endTime=2025-02-16&area=000
  // 爬取内容：采购公告
  // 爬取时间：每天23:13
  // ******
  @Cron('13 23 * * *', { timeZone: 'Asia/Shanghai' }) // 每天23点30分执行
  async crawlHuBeiGongGongZiYuan() {
    this.logger.log('爬虫开始-湖北省公共资源交易信息网');
    const isToday = true;
    // 获取今天的日期
    const today = this.getTodayString();

    const convertToStandardFormat = (compactTime) => {
      const year = compactTime.substring(0, 4);
      const month = compactTime.substring(4, 6);
      const day = compactTime.substring(6, 8);
      const hour = compactTime.substring(8, 10);
      const minute = compactTime.substring(10, 12);
      const second = compactTime.substring(12, 14);

      // 使用反引号来定义模板字符串
      const time = `${year}-${month}-${day} ${hour}:${minute}:${second}`;

      return time;
    };

    // 获取列表的函数
    const getList = async (page) => {
      try {
        const response = await axios.post(
          'https://www.hbggzyfwpt.cn/jyxxAjax/jsgcZbggNew',
          {
            currentPage: page,
            pageSize: 10,
            currentArea: '001',
            industriesTypeCode: 0,
            bulletinName: '',
            area: '000',
            publishTimeType: 4,
            publishTimeStart: today, // 固定开始时间
            publishTimeEnd: today, // 固定结束时间
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );

        const records = response.data?.data || [];
        return records;
      } catch (error) {
        console.error('获取公告列表失败:', error);
        throw error;
      }
    };

    const formatarea = (originalarea) => {
      let extractedText = '';
      if (originalarea.includes('公共资源')) {
        const parts = originalarea.split('公共资源');
        extractedText = parts[0].trim(); // 取“公共资源”前面的部分
      } else if (originalarea.includes('建设工程')) {
        const parts = originalarea.split('建设工程');
        extractedText = parts[0].trim(); // 取“建设工程”前面的部分
      } else if (originalarea.includes('云平台')) {
        const parts = originalarea.split('云平台');
        extractedText = parts[0].trim(); // 取“云平台”前面的部分
      } else if (originalarea.includes('电子交易')) {
        const parts = originalarea.split('电子交易');
        extractedText = parts[0].trim(); // 取“电子交易”前面的部分
      } else {
        extractedText = originalarea.trim(); // 如果没有匹配到关键词，则直接使用整个文本
      }
      extractedText = extractedText
        .replace('湖北随州', '随州市')
        .replace('仙桃', '仙桃市');
      return extractedText;
    };

    // 获取所有公告
    const getAllAnnouncements = async () => {
      let page = 1;
      const allData = [];

      while (isToday) {
        try {
          const records = await getList(page);

          if (records.length === 0) {
            break; // 如果当前页没有数据，停止循环
          }
          const filter = allData?.filter(
            (item) => item.title === records?.[0]?.bulletinName,
          );
          if (filter?.length > 0) {
            break;
          }

          for (const announcement of records) {
            const platformName = announcement.platform
              ? announcement.platform.platformName
              : '';
            const formattedArea = formatarea(platformName);

            const {
              tenderBulletinGuid,
              bulletinName,
              bulletinIssueTime,
              bidSectionCode,
            } = announcement;

            const url = `https://www.hbggzyfwpt.cn/jyxx/jsgcZbggDetail?guid=${tenderBulletinGuid}`;
            try {
              const detailResponse = await axios.post(
                'https://www.hbggzyfwpt.cn/jyxxAjax/jsgcZbggLiDetailT',
                {
                  bidSectionCode,
                },
                {
                  headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                  },
                },
              );

              allData.push({
                title: bulletinName,
                content: detailResponse?.data?.list?.[0]?.bulletinContent,
                publishDate: convertToStandardFormat(
                  bulletinIssueTime,
                ).substring(0, 10), // 只保留日期部分
                area: formattedArea,
                type: '1',
                url: url,
              });
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${bulletinName}:`, error);
            }
            this.logger.log(`获取公告内容成功： ${bulletinName}:`);
            await this.sleep(3000);
          }

          if (records.length !== 10) {
            break; // 如果当前页数据量小于10，停止循环
          }

          page++; // 下一页
        } catch (error) {
          console.error('Error fetching data:', error.message);
          break; // 请求出错，退出循环
        }
      }

      console.log('All announcements for today:', allData.length);
      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：浙江
  // 网站名称：浙江公共资源交易中心
  // 网站链接：https://ggzy.zj.gov.cn/jyxxgk/list.html
  // 爬取内容：中标公告
  // 爬取时间：每天23:35
  // ******
  @Cron('35 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlZhejiangPublicWinAnnouncements() {
    this.logger.log('爬虫开始-浙江公共资源交易中心-招标、交易公告');
    // 公共交易资源中心（工程建设）：项目登记、资格预审、招标公示、招标公告
    // 公共交易资源中心（国企采购）：交易公告
    // 公共交易资源中心（其他交易）：交易公告
    // 获取今天的日期
    const today = this.getTodayString();

    // 获取所有公告数据
    const getList = async (page, tab) => {
      const tabDict = {
        1: '002001008',
        2: '002001011',
        3: '002001001',
        4: '002001002',
        5: '002011001',
        6: '002007001',
      };
      return await axios.post(
        'https://ggzy.zj.gov.cn/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: (page - 1) * 12,
          rn: 12,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: 'title',
          cnum: '001',
          sort: '{"webdate":"0"}',
          ssort: 'title',
          cl: 200,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              isLike: true,
              likeType: 2,
              equal: tabDict[tab],
            },
            {
              fieldName: 'infoc',
              isLike: true,
              likeType: 2,
              equal: '33',
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: null,
          accuracy: '',
          noParticiple: '0',
          searchRange: null,
          isBusiness: '1',
        },
      );
    };
    // 获取详情页内容
    const getContent = async (url) => {
      const res = await axios.get(url);
      const $ = cheerio.load(res.data);

      // 解析详情页内容
      const content = $('table')
        .text()
        .trim()
        .replace(/\n\s*\n+/g, '\n');

      // 更新数据对象
      return content;
    };
    // 部分地区特殊处理
    const getArea = (str: string) => {
      if (str === '景宁县') {
        return '景宁畲族自治县';
      }
      return str;
    };

    // 获取所有工程建设公告的函数
    const getAllAnnouncements = async () => {
      let allData = [];
      const statusOfTab = {
        1: '项目登记',
        2: '资格预审',
        3: '招标公示',
        4: '招标公告',
        5: '交易公告',
        6: '交易公告',
      };
      for (let i = 1; i <= 6; i++) {
        let page = 1;
        const tab = i;
        let hasMoreData = true;

        while (hasMoreData) {
          const data = await getList(page, tab);
          if (
            data &&
            data.data?.result &&
            data.data?.result.records.length > 0
          ) {
            const dataArray = [];
            // 遍历每个公告，提取并处理 zhuanzai 字段
            for (const item of data.data.result.records) {
              try {
                const url = `https://ggzy.zj.gov.cn${item.linkurl}`;
                const content = await getContent(url);
                dataArray.push({
                  title: item.title,
                  content,
                  publishDate: item.infodate.split(' ')[0],
                  type: '1',
                  area: '浙江省',
                  url,
                  status: statusOfTab[tab],
                  province_name: '浙江省',
                  source:
                    '公共交易资源中心' +
                    (tab === 5
                      ? '（国企采购）'
                      : tab === 6
                      ? '（其他交易）'
                      : '（工程建设）'),
                });
                this.logger.log(`获取公告内容成功：${item.title}`);
              } catch (error) {
                this.logger.error(`获取公告内容失败： ${item.title}:`, error);
              }
            }

            allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
            if (allData?.length < data.data?.result.totalcount) {
              page++; // 增加页码
            } else {
              hasMoreData = false;
            }
          } else {
            hasMoreData = false; // 如果没有数据，停止循环
          }
        }
      }

      return allData;
    };

    const announcements = await getAllAnnouncements();
    const pushData = [];
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        const savedAnnouncement = await this.announcementService.create(item);
        if (savedAnnouncement) {
          pushData.push(savedAnnouncement);
        }
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }

    // 推送数据到外部接口
    try {
      await this.announcementService.pushAnnouncementData(
        pushData,
        'http://10.13.4.66:8054/rest/WBusinessCollect/saveInfo',
      );
      this.logger.log(`公告数据推送成功`);
    } catch (error) {
      this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
      // 注意：这里不抛出错误，避免影响公告创建流程
    }
    return true;
  }
  // ******
  // 地区：浙江
  // 网站名称：浙江公共资源交易中心
  // 网站链接：https://ggzy.zj.gov.cn/jyxxgk/list.html
  // 爬取内容：中标公告
  // 爬取时间：每天23:20
  // ******
  @Cron('20 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlZhejiangPublicWinAnnouncementsOfZhongbiao() {
    this.logger.log('爬虫开始-浙江公共资源交易中心-中标公告');

    // 获取今天的日期
    const today = this.getTodayString();

    // 获取所有公告数据
    const getList = async (page, tab) => {
      const tabDict = {
        1: '002001005',
      };
      return await axios.post(
        'https://ggzy.zj.gov.cn/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: (page - 1) * 12,
          rn: 12,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: 'title',
          cnum: '001',
          sort: '{"webdate":"0"}',
          ssort: 'title',
          cl: 200,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              isLike: true,
              likeType: 2,
              equal: tabDict[tab],
            },
            {
              fieldName: 'infoc',
              isLike: true,
              likeType: 2,
              equal: '33',
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: null,
          accuracy: '',
          noParticiple: '0',
          searchRange: null,
          isBusiness: '1',
        },
      );
    };
    // 获取详情页内容
    const getContent = async (url) => {
      const res = await axios.get(url);
      const $ = cheerio.load(res.data);

      // 解析详情页内容
      const content = $('table')
        .text()
        .trim()
        .replace(/\n\s*\n+/g, '\n');
      // 更新数据对象
      return content;
    };
    // 部分地区特殊处理
    const getArea = (str: string) => {
      if (str === '景宁县') {
        return '景宁畲族自治县';
      }
      return str;
    };
    const getTenderAnnouncementLink = async (url) => {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();

      const pageUrl = url;
      let fullUrl = '';
      let h1Text = '';
      await page.goto(pageUrl, { waitUntil: 'networkidle0' }); // 等待页面和 JS 加载完成

      // 查找包含“招标公告”的链接
      const href = await page.$$eval('a.step-item', (elements) => {
        const el = elements.find(
          (el) =>
            el.querySelector('.item-name')?.textContent?.trim() === '招标公告',
        );
        return el?.getAttribute('href') || null;
      });
      if (href) {
        fullUrl = new URL(href, pageUrl).toString();
        await page.goto(fullUrl, { waitUntil: 'networkidle0' }); // 等待页面和 JS 加载完成
        h1Text = await page.$eval('.article-info h1', (el) =>
          el.textContent?.trim(),
        );
      } else {
        console.log('未找到招标公告链接');
      }

      await browser.close();
      return {
        tender_announcement_link: fullUrl,
        tender_announcement_name: h1Text,
      };
    };
    // 获取所有工程建设公告的函数
    const getAllAnnouncements = async () => {
      let allData = [];
      const statusOfTab = {
        1: '中标结果公告',
      };
      for (let i = 1; i <= 1; i++) {
        let page = 1;
        const tab = i;
        let hasMoreData = true;

        while (hasMoreData) {
          const data = await getList(page, tab);
          if (
            data &&
            data.data?.result &&
            data.data?.result.records.length > 0
          ) {
            const dataArray = [];
            // 遍历每个公告，提取并处理 zhuanzai 字段
            for (const item of data.data.result.records) {
              try {
                const url = `https://ggzy.zj.gov.cn${item.linkurl}`;
                const content = await getContent(url);
                const { tender_announcement_link, tender_announcement_name } =
                  await getTenderAnnouncementLink(url);
                dataArray.push({
                  title: item.title,
                  content,
                  publishDate: item.infodate.split(' ')[0],
                  type: '4', //中标信息
                  area: '浙江省',
                  url,
                  status: statusOfTab[tab],
                  source: '公共交易资源中心（工程建设）',
                  province_name: '浙江省',
                  tender_announcement_link,
                  tender_announcement_name,
                });
                this.logger.log(`获取公告内容成功：${item.title}`);
              } catch (error) {
                this.logger.error(`获取公告内容失败： ${item.title}:`, error);
              }
            }

            allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
            if (allData?.length < data.data?.result.totalcount) {
              page++; // 增加页码
            } else {
              hasMoreData = false;
            }
          } else {
            hasMoreData = false; // 如果没有数据，停止循环
          }
        }
      }

      return allData;
    };

    const announcements = await getAllAnnouncements();
    const pushData = [];
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        const savedAnnouncement = await this.announcementService.create(item);
        if (savedAnnouncement) {
          pushData.push(savedAnnouncement);
        }
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }

    // 推送数据到外部接口
    try {
      await this.announcementService.pushAnnouncementDataOfZhongbiao(
        pushData,
        'http://10.13.4.66:8054/rest/WBidAnnouncementController/addBidAnnouncement',
      );
      this.logger.log(`公告数据推送成功`);
    } catch (error) {
      this.logger.error(`公告数据推送失败: ${error.message}`, error.stack);
      // 注意：这里不抛出错误，避免影响公告创建流程
    }
    return true;
  }
  // ******
  // 地区：浙江
  // 网站名称：浙江政采网
  // 网站链接：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement
  // 爬取内容：中标公告
  // 爬取时间：每天23:36
  // ******
  @Cron('36 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlZhejiangZhengcaiWinAnnouncements() {
    this.logger.log('爬虫开始-浙江政采网-中标公告');
    // 获取今天的日期
    const today = this.getTodayString();

    const getZhejiangZhengcaiArea = (str: string) => {
      if (str === '浙江') {
        return '浙江省';
      }
      if (str === '杭州市西湖风景名胜区') {
        return '西湖区';
      }
      if (str === '国家高新技术产业开发区') {
        return '国家高新区';
      }
      if (str === '前湾新区' || str === '宁波市前湾新区') {
        return '宁波市';
      }
      if (
        str === '浙江省温州市海洋经济发展示范区' ||
        str === '浙江省温州市温州生态园区'
      ) {
        return '温州市';
      }
      if (str === '嘉兴开发区') {
        return '嘉兴市';
      }
      if (str === '浙江省湖州市南太湖新区') {
        return '湖州南太湖新区';
      }
      if (str === '金华开发区') {
        return '金华经济技术开发区';
      }
      if (str === '丽水开发区') {
        return '丽水市';
      }
      if (str === '浙江省丽水市景宁畲族自治县') {
        return '景宁畲族自治县';
      }
      if (str.length > 6) {
        return str.slice(6);
      }
      return str;
    };

    // 获取所有公告数据
    const getList = async (page) => {
      return await axios.post('https://zfcg.czt.zj.gov.cn/portal/category', {
        pageNo: page,
        pageSize: 15,
        categoryCode: '110-188043',
        isGov: true,
        excludeDistrictPrefix: ['90', '006011'],
        publishDateBegin: today,
        publishDateEnd: today,
        _t: new Date().getTime(),
      });
    };
    // 获取详情页内容
    const getContent = async (url) => {
      // 点击公告链接并抓取详情页内容
      const res = await axios.get(url.replace('site', 'portal'));
      const content = res.data?.result?.data?.content;
      // 更新数据对象
      return content;
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      let page = 1;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(page);
        if (
          data &&
          data.data?.result &&
          data.data?.result.data &&
          data.data?.result.data.data.length > 0
        ) {
          const dataArray = [];
          // 遍历每个公告，提取并处理 zhuanzai 字段
          for (const item of data.data.result.data.data) {
            try {
              const date = new Date(item.publishDate);
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const url = `https://zfcg.czt.zj.gov.cn/site/detail?parentId=600007&articleId=${encodeURIComponent(
                item.articleId,
              )}`;
              const content = await getContent(url);
              dataArray.push({
                title: item.title,
                content,
                publishDate: `${year}-${month}-${day}`,
                type: '2',
                area: getZhejiangZhengcaiArea(item.districtName),
                url,
              });
              this.logger.log(`获取公告内容成功：${item.title}`);
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${item.title}:`, error);
            }
          }

          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          if (allData?.length < data.data?.result?.data?.total) {
            page++; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果没有数据，停止循环
        }
      }
      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：江西
  // 网站名称：江西省公共资源交易平台
  // 网站链接：https://www.jxsggzy.cn/jyxx/trade.html?catetype=jygg
  // 爬取内容：中标公告
  // 爬取时间：每天23:26
  // ******

  @Cron('26 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawJiangXiAnnouncements() {
    this.logger.log('爬虫开始-江西公共资源交易网');

    // 获取今天的日期
    const today = this.getTodayString();

    // 获取所有公告数据
    const getList = async (page, categoryNum) => {
      return await axios.post(
        'https://www.jxsggzy.cn/XZinterface/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: page,
          rn: 10,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: '',
          cnum: '',
          sort: '{"webdate":"0","id":"0"}',
          ssort: '',
          cl: 10000,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              equal: categoryNum,
              notEqual: null,
              equalList: null,
              notEqualList: null,
              isLike: true,
              likeType: 2,
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: [],
          accuracy: '',
          noParticiple: '1',
          searchRange: null,
          noWd: true,
        },
      );
    };

    // 获取详情页内容
    const getContent = async (url) => {
      try {
        const res = await axios.get(url);
        const $ = cheerio.load(res.data);

        // 提取内容
        const title = $('.content .title').text().trim();
        const infoTime = $('.content .infor li').text().trim();
        const textContent = $('.content .text').html(); // 获取HTML内容

        return {
          title,
          infoTime,
          content: textContent,
        };
      } catch (error) {
        this.logger.error(`获取详情页内容失败：`, error);
        return null;
      }
    };

    // 处理area名称
    const processAreaName = (xiaquname) => {
      if (xiaquname === '省本级') {
        return '江西省';
      }

      if (typeof xiaquname === 'string') {
        if (xiaquname.includes('本级')) {
          const index = xiaquname.indexOf('本级');
          return xiaquname.substring(0, index).trim();
        }

        if (xiaquname === '萍乡经济技术开发区') {
          return '萍乡市';
        }

        if (xiaquname === '仙女湖区') {
          return '新余市';
        }
        if (xiaquname === '经济技术开发区') {
          return '上饶市';
        }
        if (xiaquname === '庐陵新区') {
          return '吉安市';
        }
        if (xiaquname === '上饶高铁经济试验区') {
          return '信州区';
        }
        if (xiaquname === '高新技术产业开发区') {
          return '南昌市';
        }
        if (xiaquname === '明月山风景名胜区') {
          return '宜春市';
        }
        if (xiaquname === '三清山管理委员会') {
          return '上饶市';
        }
        if (xiaquname === '蓉江新区') {
          return '赣州市';
        }
        if (xiaquname === '开发区') {
          return '赣州市';
        }
        if (xiaquname === '高新区') {
          return '南昌市';
        }
        if (xiaquname === '赣江新区本级') {
          return '南昌市';
        }
        if (xiaquname === '陶瓷科技园') {
          return '景德镇市';
        }
        if (xiaquname === '东临新区') {
          return '抚州市';
        }
        if (xiaquname === '经开区') {
          return '吉安市';
        }
      }

      return xiaquname;
    };

    // 处理title
    const processTitle = (title) => {
      const index = title.indexOf(']');
      if (index !== -1) {
        return title.substring(index + 1).trim();
      }
      return title;
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      const categoryNums = [
        '002001001',
        '002006001',
        '002002002',
        '002003001',
        '002004001',
        '002016001',
        '002021003',
      ];
      let allData = [];

      for (const categoryNum of categoryNums) {
        let page = 0;
        let hasMoreData = true;

        while (hasMoreData) {
          try {
            const data = await getList(page, categoryNum);

            if (
              data &&
              data.data?.result &&
              data.data?.result.records.length > 0
            ) {
              const dataArray = [];
              for (const item of data.data.result.records) {
                try {
                  const date = new Date(item.webdate);
                  const year = date.getFullYear();
                  const month = String(date.getMonth() + 1).padStart(2, '0');
                  const day = String(date.getDate()).padStart(2, '0');

                  const url = `https://www.jxsggzy.cn${item.linkurl}`;
                  const detailResult = await getContent(url);

                  dataArray.push({
                    title: processTitle(item.title),
                    content: detailResult?.content, // 将内容对象转换为字符串
                    publishDate: `${year}-${month}-${day}`,
                    type: '1',
                    area: processAreaName(item.xiaquname),
                    url: url,
                  });

                  this.logger.log(
                    `获取公告内容成功：${item.title} - CategoryNum: ${categoryNum}`,
                  );
                } catch (error) {
                  this.logger.error(`获取公告内容失败： ${item.title}:`, error);
                }
                await this.sleep(3000);
              }

              allData = [...allData, ...dataArray];
              page++;
            } else {
              hasMoreData = false;
            }
          } catch (error) {
            this.logger.error(
              `获取列表失败：CategoryNum ${categoryNum}, Page ${page}`,
              error,
            );
            hasMoreData = false;
          }
        }
      }
      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(
          `保存公告成功：${item.title} - CategoryNum: ${item.categoryNum}`,
        ); // 打印 categoryNum
      } catch (error) {
        this.logger.error(
          `保存公告失败：${item.title} - CategoryNum: ${item.categoryNum}`,
          error,
        );
      }
    }
    return true;
  }

  // ******
  // 地区：福建
  // 网站名称：福建省公共资源交易电子公共服务平台
  // 网站链接：https://ggzyfw.fujian.gov.cn/business/list
  // 爬取内容：招标公告
  // 爬取时间：每天23:01
  // ******
  @Cron('01 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlFuJianBiddingAnnouncements() {
    const getContent = async (title, url) => {
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      let content = '';
      try {
        const newPage = await browser.newPage();
        await newPage.goto(url, { timeout: 60000 });
        await Promise.all([
          newPage.waitForSelector('#right-wrapper', {
            visible: true,
            timeout: 120000,
          }),
          newPage.waitForResponse(
            (response) =>
              response.url().includes('/FwPortalApi/Trade/TradeInfoContent'),
            { timeout: 120000 },
          ),
        ]);
        await this.sleep(10000);
        const html = await newPage.content();
        const $ = cheerio.load(html);

        content = $('#right-wrapper').find('.right').html();

        this.logger.log(`爬取公告内容成功：${title}`);
      } catch (error) {
        this.logger.error(`爬取公告内容失败： ${title}:`, error);
      } finally {
        await browser.close();
      }
      return content;
    };

    const getArea = (str) => {
      const result = str?.replace('【', '');
      if (result === '市本级-泉州市公共资源交易中心') {
        return '泉州市';
      } else if (result === '经济技术开发区-泉州市公共资源交易中心') {
        return '泉州市经济技术开发区';
      } else if (result === '市本级-漳州市公共资源交易中心') {
        return '漳州市';
      } else if (result === '市本级-南平市公共资源交易中心') {
        return '南平市';
      } else if (result === '市本级-莆田市行政服务中心') {
        return '莆田市';
      } else if (result === '市本级-龙岩市公共资源交易中心') {
        return '龙岩市';
      } else if (result === '市本级-三明市公共资源交易中心') {
        return '三明市';
      } else if (result === '市本级-宁德市公共资源交易中心') {
        return '宁德市';
      } else if (result.indexOf('沙县') > -1) {
        return '沙县区';
      } else if (result.indexOf('龙海市') > -1) {
        return '龙海区';
      } else if (result.indexOf('长乐市') > -1) {
        return '长乐区';
      } else if (result.indexOf('永定县') > -1) {
        return '永定区';
      } else if (result.indexOf('长泰县') > -1) {
        return '长泰区';
      }
      return result?.split('-')?.[0];
    };

    this.logger.log('爬虫开始-福建省公共资源交易电子公共服务平台-招标公告');
    // 获取今天的日期
    const today = this.getTodayString();
    const allData = [];
    let isToday = true; // 标志变量，表示是否还在抓取当天的公告
    let current = 1;

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();

    try {
      await page.goto('https://ggzyfw.fujian.gov.cn/business/list', {
        timeout: 60000,
      });
      await page.waitForSelector('.list-list', {
        visible: true,
        timeout: 120000,
      });

      // 循环抓取所有页面的公告
      while (isToday) {
        this.logger.log('开始爬第' + current + '页');

        const html = await page.content();
        const $ = cheerio.load(html);

        // 解析当前页面的公告列表
        $('.list-item').each((index, element) => {
          $(element)
            .find('.el-tag.el-tag--primary.el-tag--small.el-tag--dark')
            .remove();
          const input = $(element).find('a').text().trim();
          const splitArr = input.split('】');
          const area = getArea(splitArr?.[0]); // 地区
          const title = splitArr?.[1]?.split('\n')?.[0]; // 项目名
          const url = $(element).find('a').attr('href');
          const publishDate = $(element).find('.time').text().trim(); // 发布日期

          // 如果日期不是当天，停止抓取
          if (publishDate !== today) {
            isToday = false;
            return;
          }

          // 只抓取当天的公告
          if (title && url) {
            allData.push({
              title,
              url: `https://ggzyfw.fujian.gov.cn${url}`, // 完整链接
              publishDate,
              area,
              type: '1',
            });
          }
        });

        // 如果已经抓取完当天的所有公告，停止循环
        if (!isToday) break;

        // 检查是否存在下一页按钮
        const nextPageButton = await page.locator('.btn-next');
        if (nextPageButton) {
          // 点击下一页按钮并等待页面加载
          await Promise.all([
            nextPageButton.click(),
            page.waitForResponse(
              (response) =>
                response.url().includes('/FwPortalApi/Trade/TradeInfo'),
              { timeout: 120000 },
            ),
            await this.sleep(5000),
          ]);
        } else {
          // 如果没有下一页按钮，停止循环
          isToday = false;
        }
        current += 1;
      }

      // 遍历符合条件的公告，点击并抓取详情页内容
      for (const item of allData) {
        item.content = await getContent(item.title, item.url);
      }
    } catch (error) {
      console.error('爬取公告失败：', error);
    } finally {
      await browser.close();
    }

    for (const item of allData) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }

    return true;
  }

  // ******
  // 地区：内蒙古自治区
  // 网站名称：内蒙古自治区公共资源交易网
  // 网站链接：https://ggzyjy.nmg.gov.cn/jyxx/jyxxss/
  // 爬取内容：招标公告
  // 爬取时间：每天23:38
  // ******
  @Cron('38 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlNeiMengPublicAnnouncements() {
    this.logger.log('爬虫开始-内蒙古自治区公共资源交易网-招标公告');
    // 获取今天的日期
    const today = this.getTodayString();

    // 获取所有公告数据
    const getList = async (page) => {
      return await axios.get(
        'https://ggzyjy.nmg.gov.cn/trssearch/openSearch/searchPublishResource',
        {
          params: {
            noticeName: '',
            projectCode: '',
            bidSectionCodes: '',
            pageSize: 10,
            pageNum: page,
            noticeTypeName: '招标公告',
            platformCode: '',
            regionCode: '',
            startTime: `${today} 00:00:00`,
            endTime: `${today} 23:59:59`,
            transactionTypeName: '工程建设',
            industriesTypeName: '',
          },
        },
      );
    };
    // 获取详情页内容
    const getContent = async (sourceDataKey) => {
      const res = await axios.get(
        `https://ggzyjy.nmg.gov.cn/trssearch/openSearch/getPublishResourceDealContent?sourceDataKey=${sourceDataKey}`,
      );
      const detail = res?.data?.data?.dealContent;
      const content = `<h2 style="margin: 10px 0;">项目信息</h2><table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
      <tbody><tr><td>项目名称</td><td colspan="3">${detail.projectName}</td></tr>
      <tr><td>项目编号</td><td>${detail.projectCode}</td><td>公告类型</td><td>${detail.noticeTypeName}</td>
      </tr><tr><td>公告标段（包）数量</td><td>${detail.noticeBidNum}</td><td>公告标段（包）编号</td><td>${detail.bidSectionCodes}</td></tr>
      </tbody></table><h2 style="margin: 10px 0;">内容</h2>${detail.noticeContent}`;
      return content;
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      let page = 1;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(page);
        if (data && data.data?.data && data.data?.data.data.length > 0) {
          const dataArray = [];
          // 遍历每个公告，提取并处理 zhuanzai 字段
          for (const item of data.data.data.data) {
            try {
              const url = `https://ggzyjy.nmg.gov.cn/jyxx/index_24.html?id=${item.sourceDataKey}`;
              const content = await getContent(item.sourceDataKey);
              dataArray.push({
                title: item.noticeName,
                content,
                publishDate: item.noticeSendTime.split(' ')[0],
                type: '1',
                area: item.regionName || '内蒙古自治区',
                url,
              });
              this.logger.log(`获取公告内容成功：${item.noticeName}`);
            } catch (error) {
              this.logger.error(
                `获取公告内容失败： ${item.noticeName}:`,
                error,
              );
            }
          }

          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          if (allData?.length < data.data?.data.total) {
            page++; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果没有数据，停止循环
        }
      }
      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：新疆
  // 网站名称：新疆公共资源交易网
  // 网站链接：https://ggzy.xinjiang.gov.cn/xinjiangggzy/jyxx/tradeInfo_new.html
  // 爬取内容：招标公告
  // 爬取时间：每天23:39
  // ******
  @Cron('39 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlXinJiangPublicAnnouncements() {
    this.logger.log('爬虫开始-新疆公共资源交易网-招标公告');
    // 获取今天的日期
    const today = this.getTodayString();
    // 类别
    const equal = [
      '001001001',
      '001002001',
      '001003001',
      '001013001',
      '001004003',
      '001009001',
      '001010001',
      '001014001',
    ];
    // 获取所有公告数据
    const getList = async (pn, equal) => {
      return await axios.post(
        'https://ggzy.xinjiang.gov.cn/inteligentsearchnew/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: pn,
          rn: 10,
          sdt: `${today} 00:00:00`,
          edt: `${today} 23:59:59`,
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: 'title,projectnum,projectname',
          cnum: '001',
          sort: '{"webdate":"0"}',
          ssort: 'title',
          cl: 200,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              isLike: true,
              likeType: 2,
              equal: equal,
            },
          ],
          time: null,
          highlights: 'title',
          statistics: null,
          unionCondition: [],
          accuracy: '100',
          noParticiple: '0',
          searchRange: null,
          isBusiness: 1,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
    };
    // 判断 URL 是否是相对路径
    const isRelativeUrl = (href) => {
      if (!href) return false;

      // 排除协议开头的链接（http://, https://, mailto:, tel:, 等）
      if (/^[a-zA-Z]+:/.test(href)) {
        return false;
      }

      // 排除协议相对路径（以 // 开头）
      if (href.startsWith('//')) {
        return false;
      }
      return true;
    };
    // 获取详情页
    const getContent = async (url, linkurl) => {
      const res = await axios.get(linkurl);
      const $ = cheerio.load(res.data);
      let content = '';
      $('body a').each(function () {
        const href = $(this).attr('href');
        // 检查 href 是否是一个相对路径
        if (isRelativeUrl(href)) {
          $(this).attr('href', `https://ggzy.xinjiang.gov.cn${href}`);
        }
      });
      if (url === linkurl) {
        content = $('body .ewb-info-bd').html();
      } else {
        $('body').find('.detail-tt').remove();
        $('body').find('.detail-info').remove();
        content = $('body').html();
      }
      // 更新数据对象
      return content;
    };
    // 获取所有公告的函数
    const getAllAnnouncements = async (equal) => {
      let pn = 0;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(pn, equal);
        if (data && data.data?.result && data.data?.result.records.length > 0) {
          const dataArray = [];
          // 遍历每个公告
          for (const item of data.data.result.records) {
            try {
              const topcatenum = item.categorynum.substring(0, 6);
              let url = `https://ggzy.xinjiang.gov.cn/xinjiangggzy${item.linkurl}`;
              if (
                topcatenum == '001001' ||
                topcatenum == '001002' ||
                topcatenum == '001003' ||
                topcatenum == '001009' ||
                topcatenum == '001010'
              ) {
                if (
                  item.categorynum != '001001007' &&
                  item.categorynum != '001001008'
                ) {
                  url = `https://ggzy.xinjiang.gov.cn/xinjiangggzy/tradeInfo_detail.html?infoid=${item.infoid}&categorynum=${item.categorynum}&relationguid=${item.relationguid}`;
                }
              }
              const content = await getContent(
                url,
                `https://ggzy.xinjiang.gov.cn/xinjiangggzy${item.linkurl}`,
              );
              dataArray.push({
                title: item.title,
                content,
                publishDate: item.webdate.split(' ')[0],
                type: '1',
                area: item.xiaquname?.split('·').pop() || '新疆维吾尔自治区',
                url,
              });
              this.logger.log(`获取公告内容成功：${item.title}`);
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${item.title}:`, error);
            }
          }

          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          if (allData?.length < data.data?.result.totalcount) {
            pn = pn + 10; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果没有数据，停止循环
        }
      }
      return allData;
    };

    let announcements = [];
    for (const item of equal) {
      const arry = await getAllAnnouncements(item);
      announcements = [...announcements, ...arry];
    }
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：江苏
  // 网站名称：江苏省公共资源交易网
  // 网站链接：http://jsggzy.jszwfw.gov.cn/jyxx/tradeInfonew.html
  // 爬取内容：招标公告
  // 爬取时间：每天23:40
  // ******
  @Cron('40 23 * * *', { timeZone: 'Asia/Shanghai' }) // 每天15点执行
  async crawlJiangSuPublicAnnouncements() {
    this.logger.log('爬虫开始-江苏省公共资源交易网-招标公告');
    // 获取今天的日期
    const today = this.getTodayString();
    // 类别
    const equal = ['003001001', '003002001', '003003001', '003004002'];

    // 通过列表分页接口直接获取公告列表
    const getContent = async (url) => {
      const res = await axios.get(url);
      const $ = cheerio.load(res.data);
      const content = $('.ewb-trade-con')
        .html()
        .trim()
        .replace(/\n\s*\n+/g, '\n');
      return content;
    };

    const getList = async (pn, equal) => {
      return await axios.post(
        'http://jsggzy.jszwfw.gov.cn/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: pn,
          rn: 20,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: 'title',
          cnum: '001',
          sort: '{"infodatepx":"0"}',
          ssort: 'title',
          cl: 200,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              isLike: true,
              likeType: 2,
              equal: equal,
            },
          ],
          time: [
            {
              fieldName: 'infodatepx',
              startTime: `${today} 00:00:00`,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: 'title',
          statistics: null,
          unionCondition: null,
          accuracy: '',
          noParticiple: '1',
          searchRange: null,
          isBusiness: '1',
        },
      );
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async (equal) => {
      let pn = 0;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(pn, equal);
        if (
          data &&
          data.data?.result?.records &&
          data.data?.result?.records.length > 0
        ) {
          const dataArray = [];
          for (const item of data.data.result.records) {
            const url = `http://jsggzy.jszwfw.gov.cn${item.linkurl}`;
            try {
              const detailresponse = await getContent(url);
              dataArray.push({
                title: item.title,
                content: detailresponse,
                publishDate: item.infodateformat,
                type: '1',
                area: item.zhuanzai,
                url,
              });
              this.logger.log(`获取公告内容成功：${item.title}`);
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${item.title}:`, error);
            }
            await this.sleep(5000);
          }
          allData = [...allData, ...dataArray]; // 合并数据
          if (allData.length < data.data?.result?.totalcount) {
            pn = pn + 20; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果当前页没有数据，停止循环
        }
      }

      return allData;
    };

    let announcements = [];
    for (const item of equal) {
      const arry = await getAllAnnouncements(item);
      announcements = [...announcements, ...arry];
    }
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：四川
  // 网站名称：四川省公共资源交易信息网
  // 网站链接：https://ggzyjy.sc.gov.cn/
  // 爬取内容：中标公告
  // 爬取时间：每天23:41
  // ******
  @Cron('41 23 * * *', { timeZone: 'Asia/Shanghai' })
  async crawlSiChuanPublicWinAnnouncements() {
    this.logger.log('爬虫开始-四川省公共资源交易信息网-中标公告');
    // 获取今天的日期
    const today = this.getTodayString();
    // 通过列表分页接口直接获取公告列表

    const getList = async (pn) => {
      return await axios.post(
        'https://ggzyjy.sc.gov.cn/inteligentsearch/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: pn,
          rn: 10,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: '',
          cnum: '',
          sort: '{"webdate":"0"}',
          ssort: '',
          cl: 10000,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              equal: null,
              notEqual: null,
              equalList: ['002001008', '002002003'],
              notEqualList: null,
              isLike: true,
              likeType: 2,
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`, // 今天的日期,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: null,
          accuracy: '',
          noParticiple: '1',
          searchRange: null,
          noWd: true,
        },
      );
    };

    const processZhuanzai = (zhuanzai) => {
      // 如果 zhuanzai 为空，返回空字符串
      if (!zhuanzai) return '四川省';

      // 先按 "公共资源" 分割，取 "公共资源" 前面的内容
      const area_pub = zhuanzai.split('公共资源');
      let area = area_pub[0].trim(); // 取"公共资源"前面的内容

      // if有“政务服务",取 "政务服务" 前面的内容
      if (area.includes('政务服务')) {
        area = area.split('政务服务')[0].trim();
      }
      if (area.includes('四川')) {
        area = '四川省';
      }
      return area;
    };

    // 判断 URL 是否是相对路径
    const isRelativeUrl = (href) => {
      if (!href) return false;

      // 排除协议开头的链接（http://, https://, mailto:, tel:, 等）
      if (/^[a-zA-Z]+:/.test(href)) {
        return false;
      }

      // 排除协议相对路径（以 // 开头）
      if (href.startsWith('//')) {
        return false;
      }
      return true;
    };

    // 获取详情页
    const getContent = async (url) => {
      const res = await axios.get(url);
      const $ = cheerio.load(res.data);
      $('#infoContainer .attach_content a').each(function () {
        const href = $(this).attr('href');
        // 检查 href 是否是一个相对路径
        if (isRelativeUrl(href)) {
          $(this).attr('href', `https://ggzyjy.sc.gov.cn${href}`);
        }
      });
      const content =
        $('#newsText')
          .html()
          .trim()
          .replace(/\n\s*\n+/g, '\n') +
        $('#infoContainer .attach_content').prop('outerHTML');
      return content;
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      let pn = 0;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(pn);

        if (
          data &&
          data.data?.result?.records &&
          data.data?.result?.records.length > 0
        ) {
          const dataArray = [];
          // 遍历每个公告，提取并处理 zhuanzai 字段
          for (const item of data.data.result.records) {
            try {
              const url = `https://ggzyjy.sc.gov.cn/${item.linkurl}`;
              const content = await getContent(url);
              dataArray.push({
                title: item.title,
                content,
                publishDate: item.webdate.split(' ')[0],
                type: '2',
                area: processZhuanzai(item.zhuanzai),
                url,
              });
              this.logger.log(`成功爬取公告：${item.title}`);
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${item.title}:`, error);
            }
          }

          allData = [...allData, ...dataArray]; // 将每页的数据合并到 allData 中
          if (allData?.length < data.data?.result.totalcount) {
            pn = pn + 10; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果没有数据，停止循环
        }
      }
      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }

  // ******
  // 地区：重庆
  // 网站名称：重庆市公共资源交易网
  // 网站链接：https://www.cqggzy.com/jyxx/transaction_detail.html
  // 爬取内容：中标公告
  // 爬取时间：每天23:42
  // ******
  @Cron('42 23 * * *', { timeZone: 'Asia/Shanghai' }) // 每天15点执行
  async crawlChongQingPublicWinAnnouncements() {
    this.logger.log('爬虫开始-重庆市公共资源交易网-中标公告');
    // 获取今天的日期
    const today = this.getTodayString();

    // 通过列表分页接口直接获取公告列表
    const getContent = async (url) => {
      const res = await axios.get(url);
      const $ = cheerio.load(res.data);
      const content = $('.epoint-article-content')
        .html()
        .trim()
        .replace(/\n\s*\n+/g, '\n');
      return content;
    };

    const getList = async (pn) => {
      return await axios.post(
        'https://www.cqggzy.com/interface/rest/esinteligentsearch/getFullTextDataNew',
        {
          token: '',
          pn: pn,
          rn: 20,
          sdt: '',
          edt: '',
          wd: '',
          inc_wd: '',
          exc_wd: '',
          fields: '',
          cnum: '001',
          sort: '{"istop":"0","ordernum":"0","webdate":"0","rowid":"0"}',
          ssort: '',
          cl: 10000,
          terminal: '',
          condition: [
            {
              fieldName: 'categorynum',
              equal: null,
              notEqual: null,
              equalList: ['014001004', '014005004', '014012004', '014008003'],
              notEqualList: [
                '014001018',
                '004002005',
                '014001015',
                '014005014',
                '014008011',
              ],
              isLike: true,
              likeType: 2,
            },
          ],
          time: [
            {
              fieldName: 'webdate',
              startTime: `${today} 00:00:00`,
              endTime: `${today} 23:59:59`,
            },
          ],
          highlights: '',
          statistics: null,
          unionCondition: [],
          accuracy: '',
          noParticiple: '1',
          searchRange: null,
          noWd: true,
        },
      );
    };

    const processarea = (infoc) => {
      // 如果 infoc 为空，返回空字符串
      if (!infoc) return '重庆市';
      // 判断是否为市级
      const isCityLevel = infoc.includes('市级');
      // 如果是市级，area 为 "重庆市"，否则为 infoc 的内容
      let area = isCityLevel ? '重庆市' : infoc.trim();
      if (area === '酉阳县') {
        area = '酉阳土家族苗族自治县';
      } else if (area === '石柱县') {
        area = '石柱土家族自治县';
      } else if (area === '经开区') {
        area = '重庆市';
      }
      return area;
    };

    // 获取所有公告的函数
    const getAllAnnouncements = async () => {
      let pn = 0;
      let allData = [];
      let hasMoreData = true;

      while (hasMoreData) {
        const data = await getList(pn);
        if (
          data &&
          data.data?.result?.records &&
          data.data?.result?.records.length > 0
        ) {
          const dataArray = [];
          for (const item of data.data.result.records) {
            const { infoid, infodate, categorynum } = item;
            const date = infodate.split(' ')[0].replace(/-/g, '');
            let url = `/${categorynum}/${date}/${infoid}.html`;
            if (categorynum?.length > 9) {
              url = `https://www.cqggzy.com/xxhz/${categorynum.slice(
                0,
                6,
              )}/${categorynum.slice(0, 9)}${url}`;
            } else {
              url = `https://www.cqggzy.com/xxhz/${categorynum.slice(
                0,
                6,
              )}${url}`;
            }
            try {
              const detailresponse = await getContent(url);
              dataArray.push({
                title: item.title,
                content: detailresponse,
                publishDate: item.webdate.split(' ')[0],
                type: '2',
                area: processarea(item.infoc),
                url,
              });
              this.logger.log(`获取公告内容成功：${item.title}`);
            } catch (error) {
              this.logger.error(`获取公告内容失败： ${item.title}:`, error);
            }
          }
          allData = [...allData, ...dataArray]; // 合并数据
          if (allData.length < data.data?.result?.totalcount) {
            pn = pn + 20; // 增加页码
          } else {
            hasMoreData = false;
          }
        } else {
          hasMoreData = false; // 如果当前页没有数据，停止循环
        }
      }

      return allData;
    };

    const announcements = await getAllAnnouncements();
    for (const item of announcements) {
      try {
        // 检查是否已经有相同的公告（通过 url 字段判断）
        const existingAnnouncement = await this.announcementService.findByUrl(
          item.url,
        );

        if (existingAnnouncement) {
          this.logger.warn(
            `公告已存在，跳过保存：${item.title} (URL: ${item.url})`,
          );
          continue;
        }

        // 如果公告不存在，则创建新公告
        await this.announcementService.create(item);
        this.logger.log(`保存公告成功：${item.title}`);
      } catch (error) {
        this.logger.error(`保存公告失败：${item.title}`, error);
      }
    }
    return true;
  }
}
