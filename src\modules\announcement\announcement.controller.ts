import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Request,
  Req,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementDto, findAnnouncementDto } from './announcement.dto';
import { AuthGuard } from 'src/guards/auth.guard';

@Controller('/api/announcement')
export class AnnouncementController {
  constructor(private readonly announcementsService: AnnouncementService) {}

  @Post()
  // @UseGuards(AuthGuard)
  async create(
    @Body() createAnnouncementDto: CreateAnnouncementDto,
    @Req() req,
  ) {
    try {
      const list = createAnnouncementDto.list;
      for (const announcement of list) {
        await this.announcementsService.create(announcement);
      }
      return true;
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }

  @Post('/findByCondition')
  async findByCondition(@Body() findAnnouncementDto: findAnnouncementDto) {
    try {
      const {
        areaCode,
        keywords,
        fromDate,
        toDate,
        currentPage,
        pageSize,
        type,
        searchWord,
      } = findAnnouncementDto;
      return this.announcementsService.findAnnouncementsByConditionDateRange(
        searchWord,
        type,
        areaCode,
        keywords,
        fromDate,
        toDate,
        currentPage,
        pageSize,
      );
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  async findOne(@Query('id') id: string) {
    return this.announcementsService.findOne(id);
  }

  @Post('/getIndustry')
  async getIndustry(
    @Body() industryDto: { projectName: string; constructionUnit: string },
  ) {
    try {
      const { projectName, constructionUnit } = industryDto;
      const industry = this.announcementsService.getIndustryByProjectAndUnit(
        projectName,
        constructionUnit,
      );

      return {
        success: true,
        industry: industry || null,
        message: industry ? `匹配到行业: ${industry}` : '未匹配到相关行业',
      };
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }
}
