import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Request,
  Req,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementDto, findAnnouncementDto } from './announcement.dto';
import { AuthGuard } from 'src/guards/auth.guard';

@Controller('/api/announcement')
export class AnnouncementController {
  constructor(private readonly announcementsService: AnnouncementService) {}

  @Post()
  // @UseGuards(AuthGuard)
  async create(
    @Body() createAnnouncementDto: CreateAnnouncementDto,
    @Req() req,
  ) {
    try {
      const list = createAnnouncementDto.list;
      for (const announcement of list) {
        await this.announcementsService.create(announcement);
      }
      return true;
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }

  @Post('/findByCondition')
  async findByCondition(@Body() findAnnouncementDto: findAnnouncementDto) {
    try {
      const { areaCode, keywords, fromDate, toDate, currentPage, pageSize, type, searchWord } =
        findAnnouncementDto;
      return this.announcementsService.findAnnouncementsByConditionDateRange(
        searchWord,
        type,
        areaCode,
        keywords,
        fromDate,
        toDate,
        currentPage,
        pageSize,
      );
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  async findOne(@Query('id') id: string) {
    return this.announcementsService.findOne(id);
  }

  @Post('/queryAndPush')
  async queryAndPush(
    @Body() queryDto: {
      publishDate?: string;
      status?: string;
      type?: string;
    },
  ) {
    try {
      const { publishDate, status, type } = queryDto;
      return await this.announcementsService.queryAndPushAnnouncements(
        publishDate,
        status,
        type,
      );
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }

  @Post('/queryAndPushByDateRange')
  async queryAndPushByDateRange(
    @Body() queryDto: {
      fromDate: string;
      toDate: string;
      status?: string;
      type?: string;
    },
  ) {
    try {
      const { fromDate, toDate, status, type } = queryDto;
      return await this.announcementsService.queryAndPushAnnouncementsByDateRange(
        fromDate,
        toDate,
        status,
        type,
      );
    } catch (err) {
      throw new HttpException(err, err.status || HttpStatus.BAD_REQUEST);
    }
  }
}
