import {
  CallH<PERSON>ler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { getReqMainInfo } from 'src/utils/getReqMainInfo';
@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const req = ctx.getRequest();
    req.logger = new Logger(
      `${req.method} ${req.originalUrl} [${req.hostname}]`,
    );
    return next.handle().pipe(
      map((data) => {
        req.logger.log(`\nReq: ${JSON.stringify(getReqMainInfo(req))}\n`);
        return { success: true, code: 200, data, errMsg: null };
      }),
    );
  }
}
