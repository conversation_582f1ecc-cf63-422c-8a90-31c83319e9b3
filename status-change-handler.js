const axios = require('axios');
const pm2 = require('pm2');

const statusText = {
  exit: '退出',
  stop: '停止',
  restart: '重启',
};

// 企业微信机器人 Webhook URL
const webhookUrl =
  'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b49dde41-d37b-4784-b289-850f77b53393';

// 连接到 PM2
pm2.connect((err) => {
  if (err) {
    console.error('无法连接到 PM2:', err);
    process.exit(1);
  }

  console.log('已连接到 PM2');

  // 监听 PM2 事件
  pm2.launchBus((err, bus) => {
    if (err) {
      console.error('无法启动 PM2 事件总线:', err);
      process.exit(1);
    }

    console.log('已启动 PM2 事件总线');

    // 监听应用状态变化事件
    bus.on('process:event', (packet) => {
      const { event, process } = packet;
      if (event === 'exit' || event === 'stop' || event === 'restart') {
        const message = `Nest.js 服务状态变化: 【${
          statusText[event]
        }】\n服务名称: ${process.name}\n进程ID: ${
          process.pm_id
        }\n时间: ${new Date().toLocaleString()}`;

        // 调用企业微信 API 发送通知
        axios
          .post(webhookUrl, {
            msgtype: 'text',
            text: {
              content: message,
            },
          })
          .then((response) => {
            console.log('通知发送成功:', response.data);
          })
          .catch((error) => {
            console.error('通知发送失败:', error);
          });
      }
    });
  });
});
