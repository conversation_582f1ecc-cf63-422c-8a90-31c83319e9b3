import { Controller, Get, Query } from '@nestjs/common';
import { WechatService } from './wechat.service';

@Controller('/api/wechat')
export class WechatController {
  constructor(private wechatService: WechatService) {}

  @Get('login')
  async login(@Query('code') code: string): Promise<any> {
    if (!code) {
      throw new Error('Authorization code is required');
    }

    const userId = await this.wechatService.getUserId(code);
    const userDetail = await this.wechatService.getUserDetail(userId);

    return userDetail;
  }
}