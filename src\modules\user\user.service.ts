import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from './user.schema';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UserService {
  constructor(@InjectModel(User.name) private userModel: Model<User>) {}

  async validatePassword(
    providedPassword: string,
    password: string,
  ): Promise<boolean> {
    return await bcrypt.compare(providedPassword, password);
  }

  async findUserByUsername(username: string): Promise<User | null> {
    return this.userModel.findOne({ username }).exec();
  }

  async findUserById(userId: string): Promise<User | null> {
    return this.userModel.findOne({ _id: userId }).exec();
  }

  async createUser(username: string, password: string): Promise<User> {
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = new this.userModel({
      username,
      password: hashedPassword,
      email: username,
    });
    return user.save();
  }

  async changePassword(
    userId: string,
    oldPassword: string,
    newPassword: string,
  ): Promise<void> {
    const user = await this.userModel.findById(userId).exec();
    if (!user) {
      throw new HttpException('用户不存在', HttpStatus.BAD_REQUEST);
    } else {
      const isPasswordValid = await this.validatePassword(
        oldPassword,
        user.password,
      );
      if (!isPasswordValid) {
        throw new HttpException('旧密码校验错误', HttpStatus.BAD_REQUEST);
      } else {
        user.password = await bcrypt.hash(newPassword, 10);
        await user.save();
      }
    }
  }

  async resetPassword(username: string): Promise<void> {
    const user = await this.userModel.findOne({ username }).exec();
    if (!user) {
      throw new HttpException('用户不存在', HttpStatus.BAD_REQUEST);
    }
    user.password = await bcrypt.hash('hxpti@123', 10);
    await user.save();
  }
}
