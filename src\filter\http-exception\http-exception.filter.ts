import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const req = ctx.getRequest<Request>(); // 获取请求对象
    const res = ctx.getResponse<Response>();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const message = exception.message;

    const logger = new Logger(
      `${req.method} ${req.originalUrl} [${req.hostname}]`,
    );
    logger.error({
      exception,
      method: req.method,
      path: req.path,
      headers: req.headers,
    });
    res.status(status).json({
      success: false,
      code: status,
      data: null,
      errMsg: message,
    });
  }
}
