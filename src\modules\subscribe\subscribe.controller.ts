// src/subscribe/subscribe.controller.ts
import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Request,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { SubscribeService } from './subscribe.service';
import { CreateSubscribeDto, UpdateSubscribeDto } from './subscribe.dto';
import { AuthGuard } from 'src/guards/auth.guard';
import { UserService } from '../user/user.service';

@Controller('/api/subscribe')
export class SubscribeController {
  constructor(
    private readonly subscribeService: SubscribeService,
    private readonly userService: UserService, // private readonly subscribeCronService: SubscribeCronService,
  ) {}

  @Post()
  @UseGuards(AuthGuard)
  @UsePipes(new ValidationPipe())
  async create(
    @Body() createSubscribeDto: CreateSubscribeDto,
    @Request() req,
  ): Promise<boolean> {
    try {
      const userId = req.user.userId;
      const subscribe = await this.subscribeService.findByUserId(userId, createSubscribeDto.type);

      const createdSubscribe = async() => {
        const { email } = await this.userService.findUserById(userId);
        return this.subscribeService.create({
          ...createSubscribeDto,
          userId,
          email,
        });
      }
      if (subscribe) {
        if (subscribe.type && createSubscribeDto.type === subscribe.type) {
          return this.subscribeService.updateByUserId(
            req.user.userId,
            createSubscribeDto,
            Boolean(subscribe.type)
          );
        } else {
          await createdSubscribe()
        }
      } else {
        await createdSubscribe()
      }
    } catch (error) {
      throw new HttpException(error, error.status || HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  @UseGuards(AuthGuard)
  async findByUserId(@Request() req, @Query('type') type: string): Promise<CreateSubscribeDto> {
    return this.subscribeService.findByUserId(req.user.userId, type);
  }

  // @Post()
  // async sendEmail(@Request() req): Promise<any> {
  //   return this.subscribeCronService.sendDailyAnnouncements();
  // }

  @Delete(':id')
  @UseGuards(AuthGuard)
  async deleteById(@Param('id') id: string): Promise<void> {
    return this.subscribeService.deleteById(id);
  }

  // @Post()
  // @UsePipes(new ValidationPipe())
  // async updateByUserId(
  //   @Request() req,
  //   @Body() updateSubscribeDto: UpdateSubscribeDto,
  // ): Promise<boolean> {
  //   return this.subscribeService.updateByUserId(
  //     req.user.userId,
  //     updateSubscribeDto,
  //   );
  // }
}
