import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class City extends Document {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop({ required: true })
  code: string;

  @Prop({ required: true })
  provinceCode: string;
}
export type CityDocument = City & Document;
export const CitySchema = SchemaFactory.createForClass(City);
