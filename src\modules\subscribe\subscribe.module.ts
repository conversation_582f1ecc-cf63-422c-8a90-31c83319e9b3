// src/subscribe/subscribe.module.ts
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SubscribeService } from './subscribe.service';
import { SubscribeController } from './subscribe.controller';
import { Subscribe, SubscribeSchema } from './subscribe.schema';
import { UserModule } from '../user/user.module';
import { AreaModule } from '../area/area.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Subscribe.name, schema: SubscribeSchema },
    ]),
    UserModule,
    AreaModule,
  ],
  providers: [SubscribeService],
  controllers: [SubscribeController],
  exports: [SubscribeService],
})
export class SubscribeModule {}
