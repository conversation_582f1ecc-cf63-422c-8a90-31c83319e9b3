// src/subscribe/subscribe.service.ts
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Subscribe } from './subscribe.schema';
import { CreateSubscribeDto, UpdateSubscribeDto } from './subscribe.dto';
import { AreaService } from '../area/area.service';

@Injectable()
export class SubscribeService {
  constructor(
    @InjectModel(Subscribe.name) private subscribeModel: Model<Subscribe>,
    private readonly areaService: AreaService,
  ) {}

  async create(subscribe: CreateSubscribeDto): Promise<boolean> {
    const createdSubscribe = new this.subscribeModel(subscribe);
    try {
      await createdSubscribe.save();
      return true;
    } catch (error) {
      console.error('Error creating subscription:', error);
      return false;
    }
  }

  async findByUserId(userId: string, type: string): Promise<any> {
    let res = await this.subscribeModel.findOne({ userId, type }).exec();
    if (!res) {
      res = await this.subscribeModel.findOne({ userId }).exec();
    }
    if (res) {
      const { _id, areaCode, keywords, type } = res;
      return {
        _id,
        areaCode,
        keywords,
        areaName: await this.areaService.getAreaNameByCode(areaCode),
        userId,
        type,
      };
    }
    return null;
  }

  async findAll(): Promise<Subscribe[]> {
    return this.subscribeModel.find().exec();
  }

  async deleteById(id: string): Promise<void> {
    await this.subscribeModel.findByIdAndDelete(id).exec();
  }

  async updateByUserId(
    userId: string,
    updateSubscribeDto: UpdateSubscribeDto,
    hasType: boolean
  ): Promise<boolean> {
    const query = { userId }
    if (hasType) {
      query['type'] = updateSubscribeDto.type;
    }
    const res = await this.subscribeModel
      .updateOne(query, updateSubscribeDto, { new: true })
      .exec();

    // 检查是否有文档被更新
    return res.acknowledged && res.modifiedCount > 0;
  }
}
