福建省公共资源交易电子公共服务平台，接口解密方法。

```
import CryptoJS from 'crypto-js'

const e = CryptoJS.enc.Utf8.parse('EB444973714E4A40876CE66BE45D5930')
const n = CryptoJS.enc.Utf8.parse('B5A8904209931867')
const decrypted = CryptoJS.AES.decrypt(encodedData, e, {
    iv: n,
    mode: CryptoJS.mode.CBC, // 加密模式
    padding: CryptoJS.pad.Pkcs7, // 填充方式
})
// 将解密后的数据转换为 UTF-8 字符串
const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)

console.log('解密后的文本:', decryptedText ? JSON.parse(decryptedText) : decryptedText)
```