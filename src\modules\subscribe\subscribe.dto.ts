import { IsString, <PERSON><PERSON>ot<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>rray } from 'class-validator';

export class CreateSubscribeDto {
  @IsString()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  areaCode: string;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  keywords: string[];

  @IsString()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  type: string;
}

export class UpdateSubscribeDto {
  @IsString()
  userId?: string;

  @IsString()
  @IsOptional()
  areaCode?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  keywords?: string[];

  @IsString()
  @IsOptional()
  type: string;
}
