export class CreateAnnouncementDto {
  list: AnnouncementDto[]; // 通知列表
}

export class AnnouncementDto {
  title: string;
  content: string;
  publishDate: string;
  type: string;
  area: string;
  url: string;
}

export class findAnnouncementDto {
  searchWord: string;
  type: string;
  areaCode: string;
  fromDate: string;
  toDate: string;
  keywords: string[];
  currentPage: number;
  pageSize: number;
}

export class UpdateAnnouncementDto {
  id?: string;
  title?: string;
  content?: string;
  publishDate?: string;
  type?: string;
  area?: string;
  url?: string;
}
