import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Area, AreaDocument } from './area.schema';

@Injectable()
export class AreaService {
  constructor(@InjectModel(Area.name) private areaModel: Model<AreaDocument>) {}

  async getAllAreas(): Promise<any[]> {
    const areas = await this.areaModel.find().lean();
    const tree = this.arrayToTree(areas);
    return tree;
  }

  async getAreaCodeByName(name: string): Promise<string | undefined> {
    const area = await this.areaModel.findOne({ name }).exec();
    return area ? area.code : undefined;
  }

  async getAreaNameByCode(code: string): Promise<string | undefined> {
    if (code === '') return '全国';
    const area = await this.areaModel.findOne({ code }).exec();
    return area ? area.name : undefined;
  }

  arrayToTree(data) {
    // 创建一个映射表，用于快速查找
    const map = {};
    data.forEach((item) => {
      map[item.code] = { ...item, children: [] };
    });

    // 创建一个数组，用于存储根节点
    const tree = [];

    // 遍历数据，构建树形结构
    data.forEach((item) => {
      const node = map[item.code];

      // 如果是省份，直接添加到树中
      if (!item.provinceCode) {
        tree.push(node);
      } else {
        // 否则，找到父节点并将其添加为子节点
        const parentCode = item.cityCode || item.provinceCode;
        const parentNode = map[parentCode];
        if (parentNode) {
          parentNode.children.push(node);
        }
      }
    });

    return tree;
  }
}
