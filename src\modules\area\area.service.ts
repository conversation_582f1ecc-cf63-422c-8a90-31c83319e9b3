import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Area, AreaDocument } from './area.schema';

@Injectable()
export class AreaService {
  constructor(@InjectModel(Area.name) private areaModel: Model<AreaDocument>) {}

  async getAllAreas(): Promise<any[]> {
    const areas = await this.areaModel.find().lean();
    const tree = this.arrayToTree(areas);
    return tree;
  }

  async getAreaCodeByName(name: string): Promise<string | undefined> {
    const area = await this.areaModel.findOne({ name }).exec();
    return area ? area.code : undefined;
  }

  async getAreaNameByCode(code: string): Promise<string | undefined> {
    if (code === '') return '全国';
    const area = await this.areaModel.findOne({ code }).exec();
    return area ? area.name : undefined;
  }

  /**
   * 根据区域代码获取省市区完整路径
   * @param areaCode 区域代码
   * @returns 省市区路径，用-分隔，如"山西省-临汾市-霍州市"
   */
  async getProvinceCity(areaCode: string): Promise<string | null> {
    if (!areaCode || areaCode === '') {
      return null;
    }

    try {
      // 查找当前区域
      const currentArea = await this.areaModel
        .findOne({ code: areaCode })
        .exec();
      if (!currentArea) {
        return null;
      }

      const pathParts: string[] = [];

      // 如果有省份代码，查找省份
      if (currentArea.provinceCode) {
        const province = await this.areaModel
          .findOne({
            code: currentArea.provinceCode,
          })
          .exec();
        if (province) {
          pathParts.push(province.name);
        }
      }

      // 如果有城市代码且不等于省份代码，查找城市
      if (
        currentArea.cityCode &&
        currentArea.cityCode !== currentArea.provinceCode
      ) {
        const city = await this.areaModel
          .findOne({
            code: currentArea.cityCode,
          })
          .exec();
        if (city) {
          pathParts.push(city.name);
        }
      }

      // 添加当前区域名称
      pathParts.push(currentArea.name);

      return pathParts.join('-');
    } catch (error) {
      console.error('获取省市区路径失败:', error);
      return null;
    }
  }

  arrayToTree(data) {
    // 创建一个映射表，用于快速查找
    const map = {};
    data.forEach((item) => {
      map[item.code] = { ...item, children: [] };
    });

    // 创建一个数组，用于存储根节点
    const tree = [];

    // 遍历数据，构建树形结构
    data.forEach((item) => {
      const node = map[item.code];

      // 如果是省份，直接添加到树中
      if (!item.provinceCode) {
        tree.push(node);
      } else {
        // 否则，找到父节点并将其添加为子节点
        const parentCode = item.cityCode || item.provinceCode;
        const parentNode = map[parentCode];
        if (parentNode) {
          parentNode.children.push(node);
        }
      }
    });

    return tree;
  }
}
