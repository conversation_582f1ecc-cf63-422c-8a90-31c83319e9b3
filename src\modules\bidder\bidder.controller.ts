import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Request,
  Req,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { BidderService } from './bidder.service';
import { findBidderDto } from './bidder.dto';

@Controller('/api/bidder')
export class BidderController {
    constructor(private readonly bidderService: BidderService) {}

    @Post('/findSumInfo')
    async findSumInfo(@Body() findBidderDto: findBidderDto) {
        try {
            const { companyA,companyB,industry,fromDate,toDate,province} = 
            findBidderDto;
            return await this.bidderService.findSumInfo(
                companyA,
                companyB,
                industry,
                fromDate,
                toDate,
                province,
            );
        } catch (error) {
            return error;
        }
    }
}
