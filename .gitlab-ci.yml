image: node:20

stages: # 分段
  - install
  - build
  - deploy
  # - reload-nginx


cache: # 缓存
  key: ${CI_BUILD_REF_NAME}
  paths:
    - node_modules

install-job:
  stage: install
  only:
    - dev
    - test
    - uat
    - prod
    - master
  tags:
    - general
  script:
    - node -v
    - npm -v
    - echo "🧩 开始install"
    - yarn --registry=http://***********:4873
    - OUTPUT_PATH=/data/${CI_PROJECT_NAME}/${CI_COMMIT_REF_NAME}
    - echo "$OUTPUT_PATH"
    - |
      if [ ! -d ${OUTPUT_PATH} ]; then
        mkdir -p ${OUTPUT_PATH}
      fi 
    - cp -rf node_modules/ ${OUTPUT_PATH}
    - ls "$OUTPUT_PATH"

build-job:
  stage: build
  only:
    - dev
    - test
    - uat
    - prod
    - master
  tags:
    - general
  before_script: 
    - mkdir -p /data
  artifacts:
    name: "dist"
    expire_in: 60 mins
    paths:
      - dist/
  script:
    - echo "⏰ 开始代码打包"
    - yarn build
    - pwd

# ========== 部署基类（供继承）==========
.deploy-job:
  stage: deploy
  tags:
    - general
  before_script:
    - mkdir -p /data/castle-data
  when: manual
  script:
    - echo "🖥️ 当前部署机器 hostname：$(hostname)"
    - echo "🔌 IP 地址:"
    - hostname -I || ip addr show
    - echo "📁 当前路径：$(pwd)"
    - echo "🧑 用户名：$(whoami)"

    - echo "🚌 正在部署到宿主机目录"
    - OUTPUT_PATH=/data/${CI_PROJECT_NAME}/${CI_COMMIT_REF_NAME}
    - echo "$OUTPUT_PATH"
    - ls /data/platform/master
    - |
      if [ ! -d ${OUTPUT_PATH} ]; then
        mkdir -p ${OUTPUT_PATH}
      fi 
    - cp -rf ./ ${OUTPUT_PATH}
    - cp -rf .env ${OUTPUT_PATH}/dist
    # - cp -rf .env.* ${OUTPUT_PATH}
    - rm -rf ${OUTPUT_PATH}/nginx-*.conf

dev-deploy-job:
  only:
    - dev
  extends: .deploy-job
  when: always

test-deploy-job:
  only:
    - test
  extends: .deploy-job

uat-deploy-job:
  only:
    - uat
  extends: .deploy-job

prod-deploy-job:
  only:
    - prod
  extends: .deploy-job

master-deploy-job:
  only:
    - master
  extends: .deploy-job

# reload-nginx:
#   stage: reload-nginx
#   only:
#     - dev
#     - test
#     - uat
#     - prod
#     - master
#   tags:
#     - general
#   when: on_success
#   script:
#     - echo "🧭 重启nginx服务"
#     - OUTPUT_PATH=/data/${CI_PROJECT_NAME}
#     - cp -rf dist/nginx-*.conf ${OUTPUT_PATH}
#     - echo $(date "+%Y-%m-%d %H:%M:%S") "完成"【${CI_PROJECT_NAME}】"的部署，重启nginx服务" >> /data/nginx-reload-log.log
#     - echo "🎉 完成！"
