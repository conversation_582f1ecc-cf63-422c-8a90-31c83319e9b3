import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BidderController } from './bidder.controller';
import { BidderService } from './bidder.service';
import { Announcement, AnnouncementSchema } from '../announcement/announcement.schema';
import { AreaModule } from '../area/area.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Announcement.name, schema: AnnouncementSchema },
    ]),
    AreaModule,
  ],
  controllers: [BidderController],
  providers: [BidderService]
})
export class BidderModule {}
