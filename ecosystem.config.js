module.exports = {
  apps: [
    {
      name: 'hx-business-oppotunity-server',
      script: './dist/main.js',
      watch: true,
      ignore_watch: ['node_modules', 'dist/logs', './dist/logs', 'logs'],
      max_memory_restart: '1G', // 设置日志文件的最大大小
      log_date_format: 'YYYY-MM-DD HH:mm:ss', // 设置日志时间格式
      log_rotate_interval: '3d', // 设置日志轮换间隔
    },
    {
      name: 'status-change-handler', // 状态变化监听脚本
      script: './status-change-handler.js', // 监听脚本路径
      interpreter: 'node', // 使用 Node.js 运行
      watch: false, // 不监听文件变化
      autorestart: true, // 自动重启
    },
  ],
};
