import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Announcement, AnnouncementDocument } from '../announcement/announcement.schema';
import { AreaService } from '../area/area.service';

@Injectable()
export class BidderService {
    constructor(
        @InjectModel(Announcement.name)
        private announcementModel: Model<AnnouncementDocument>,
        private readonly areaService: AreaService,
    ) {}

    async findSumInfo(companyA: string, companyB: string, industry: string, fromDate: string, toDate: string, province: string): Promise<any> {

        // 构建基础查询条件
        const baseQuery: any = {
            type: '2' // 添加type=2的条件
        };

        // 添加时间范围筛选
        if(fromDate && toDate) {
            baseQuery.publishDate = {
                $gte: new Date(fromDate), // 开始时间（UTC）
                $lte: new Date(toDate), // 结束时间（UTC）
            };
        }

        // 添加行业筛选（数据库中行业是逗号分隔的，使用正则匹配包含该行业的记录）
        if(industry) {
            // 使用正则表达式匹配包含该行业的记录
            baseQuery.industry = { $regex: new RegExp(`(^|,)\\s*${industry}\\s*(,|$)`), $options: 'i' };
        }

        // 添加省份筛选
        if(province) {
            // 根据省份名称获取省份代码，然后筛选该省份下的所有区域
            const provinceCode = await this.areaService.getAreaCodeByName(province);
            if(provinceCode) {
                baseQuery.areaCode = { $regex: `^${provinceCode}`, $options: 'i' };
            }
        }

        // 查询companyA的数据
        const companyAQuery = {
            ...baseQuery,
            winningBidder: companyA
        };

        // 查询companyB的数据
        const companyBQuery = {
            ...baseQuery,
            winningBidder: companyB
        };

        // 定义所有行业类型（除"其他"外）
        const allIndustries = ['军政', '网信安', '交通能源', '医卫', '企业大客户', '园区', '数据中心'];

        if (industry) {
            // 有行业筛选条件时，执行原有的查询逻辑
            return await this.findSumInfoWithIndustry(companyA, companyB, companyAQuery, companyBQuery, baseQuery);
        } else {
            // 无行业筛选条件时，返回各行业占比统计
            return await this.findSumInfoByIndustries(companyA, companyB, baseQuery, allIndustries);
        }
    }

    // 有行业筛选条件时的查询方法
    private async findSumInfoWithIndustry(companyA: string, companyB: string, companyAQuery: any, companyBQuery: any, baseQuery: any): Promise<any> {
        const [companyAResult, companyBResult, totalCountResult, companyAMonthlyResult, companyBMonthlyResult] = await Promise.all([
            this.announcementModel.aggregate([
                { $match: companyAQuery },
                {
                    $group: {
                        _id: null,
                        count: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                }
            ]).exec(),
            this.announcementModel.aggregate([
                { $match: companyBQuery },
                {
                    $group: {
                        _id: null,
                        count: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                }
            ]).exec(),
            // 查询满足基础条件的总条数（不限制winningBidder，但排除行业为"其他"的记录）
            this.announcementModel.aggregate([
                {
                    $match: {
                        ...baseQuery,
                        industry: { $ne: "其他" } // 排除行业为"其他"的记录
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalCount: { $sum: 1 }
                    }
                }
            ]).exec(),
            // 查询companyA的每月统计数据
            this.announcementModel.aggregate([
                { $match: companyAQuery },
                {
                    $group: {
                        _id: {
                            year: { $year: "$publishDate" },
                            month: { $month: "$publishDate" }
                        },
                        count: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                },
                {
                    $sort: { "_id.year": 1, "_id.month": 1 }
                }
            ]).exec(),
            // 查询companyB的每月统计数据
            this.announcementModel.aggregate([
                { $match: companyBQuery },
                {
                    $group: {
                        _id: {
                            year: { $year: "$publishDate" },
                            month: { $month: "$publishDate" }
                        },
                        count: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                },
                {
                    $sort: { "_id.year": 1, "_id.month": 1 }
                }
            ]).exec()
        ]);

        // 处理结果，如果没有数据则返回默认值
        const companyAData = companyAResult.length > 0 ? companyAResult[0] : { count: 0, totalAmount: 0 };
        const companyBData = companyBResult.length > 0 ? companyBResult[0] : { count: 0, totalAmount: 0 };
        const totalCount = totalCountResult.length > 0 ? totalCountResult[0].totalCount : 0;

        // 计算比例（保留两位小数）
        const companyAPercentage = totalCount > 0 ? Number(((companyAData.count / totalCount) * 100).toFixed(2)) : 0;
        const companyBPercentage = totalCount > 0 ? Number(((companyBData.count / totalCount) * 100).toFixed(2)) : 0;

        // 处理每月统计数据
        const formatMonthlyData = (monthlyResult: any[]) => {
            return monthlyResult.map(item => ({
                year: item._id.year,
                month: item._id.month,
                monthKey: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`, // 格式化为 YYYY-MM
                count: item.count,
                totalAmount: item.totalAmount
            }));
        };

        const companyAMonthlyData = formatMonthlyData(companyAMonthlyResult);
        const companyBMonthlyData = formatMonthlyData(companyBMonthlyResult);

        return {
            companyA: {
                name: companyA,
                count: companyAData.count,
                totalAmount: companyAData.totalAmount,
                percentage: companyAPercentage, // 占总条数的比例
                monthlyData: companyAMonthlyData // 每月统计数据
            },
            companyB: {
                name: companyB,
                count: companyBData.count,
                totalAmount: companyBData.totalAmount,
                percentage: companyBPercentage, // 占总条数的比例
                monthlyData: companyBMonthlyData // 每月统计数据
            },
            summary: {
                totalCount: totalCount, // 满足条件的总条数
                companyAAndBCount: companyAData.count + companyBData.count, // 两个公司的总条数
                companyAAndBTotalAmount: companyAData.totalAmount + companyBData.totalAmount, // 两个公司的总金额
                companyAAndBPercentage: totalCount > 0 ? Number((((companyAData.count + companyBData.count) / totalCount) * 100).toFixed(2)) : 0 // 两个公司占总条数的比例
            }
        };
    }

    // 无行业筛选条件时，按行业统计各公司占比的方法
    private async findSumInfoByIndustries(companyA: string, companyB: string, baseQuery: any, allIndustries: string[]): Promise<any> {
        // 先获取两个公司的总中标项目数（排除行业为"其他"的数据）
        const [companyATotalResult, companyBTotalResult] = await Promise.all([
            this.announcementModel.aggregate([
                {
                    $match: {
                        ...baseQuery,
                        winningBidder: companyA,
                        industry: { $ne: "其他" } // 排除行业为"其他"的记录
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalCount: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                }
            ]).exec(),
            this.announcementModel.aggregate([
                {
                    $match: {
                        ...baseQuery,
                        winningBidder: companyB,
                        industry: { $ne: "其他" } // 排除行业为"其他"的记录
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalCount: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                }
            ]).exec()
        ]);

        const companyATotalData = companyATotalResult.length > 0 ? companyATotalResult[0] : { totalCount: 0, totalAmount: 0 };
        const companyBTotalData = companyBTotalResult.length > 0 ? companyBTotalResult[0] : { totalCount: 0, totalAmount: 0 };

        // 查询两个公司在各个行业的统计数据（包括每月趋势）
        const industryPromises = allIndustries.map(async (industryName) => {
            const industryQuery = {
                ...baseQuery,
                industry: { $regex: new RegExp(`(^|,)\\s*${industryName}\\s*(,|$)`), $options: 'i' }
            };

            const [companyAIndustryResult, companyBIndustryResult] = await Promise.all([
                // companyA在该行业的统计
                this.announcementModel.aggregate([
                    { $match: { ...industryQuery, winningBidder: companyA } },
                    {
                        $group: {
                            _id: null,
                            count: { $sum: 1 },
                            totalAmount: {
                                $sum: {
                                    $toDouble: {
                                        $ifNull: ["$winningBidAmount", 0]
                                    }
                                }
                            }
                        }
                    }
                ]).exec(),
                // companyB在该行业的统计
                this.announcementModel.aggregate([
                    { $match: { ...industryQuery, winningBidder: companyB } },
                    {
                        $group: {
                            _id: null,
                            count: { $sum: 1 },
                            totalAmount: {
                                $sum: {
                                    $toDouble: {
                                        $ifNull: ["$winningBidAmount", 0]
                                    }
                                }
                            }
                        }
                    }
                ]).exec()
            ]);

            const companyAIndustryData = companyAIndustryResult.length > 0 ? companyAIndustryResult[0] : { count: 0, totalAmount: 0 };
            const companyBIndustryData = companyBIndustryResult.length > 0 ? companyBIndustryResult[0] : { count: 0, totalAmount: 0 };

            // 计算各行业在各自公司总项目中的占比
            const companyAPercentage = companyATotalData.totalCount > 0 ? Number(((companyAIndustryData.count / companyATotalData.totalCount) * 100).toFixed(2)) : 0;
            const companyBPercentage = companyBTotalData.totalCount > 0 ? Number(((companyBIndustryData.count / companyBTotalData.totalCount) * 100).toFixed(2)) : 0;

            return {
                industry: industryName,
                companyA: {
                    count: companyAIndustryData.count,
                    totalAmount: companyAIndustryData.totalAmount,
                    percentage: companyAPercentage // 该行业在companyA所有项目中的占比
                },
                companyB: {
                    count: companyBIndustryData.count,
                    totalAmount: companyBIndustryData.totalAmount,
                    percentage: companyBPercentage // 该行业在companyB所有项目中的占比
                }
            };
        });

        const industryResults = await Promise.all(industryPromises);

        // 获取总条数查询（排除"其他"行业）
        const totalCountResult = await this.announcementModel.aggregate([
            {
                $match: {
                    ...baseQuery,
                    industry: { $ne: "其他" } // 排除行业为"其他"的记录
                }
            },
            {
                $group: {
                    _id: null,
                    totalCount: { $sum: 1 }
                }
            }
        ]).exec();

        const grandTotal = totalCountResult.length > 0 ? totalCountResult[0].totalCount : 0;

        // 获取市场占有率数据（按行业分组）、省份分布数据和总体每月趋势数据
        const [marketShareData, provinceDistributionData, companyAMonthlyResult, companyBMonthlyResult] = await Promise.all([
            this.getMarketShareByIndustry(companyA, companyB, baseQuery, allIndustries),
            this.getProvinceDistributionByIndustry(companyA, companyB, baseQuery, allIndustries),
            // companyA的总体每月统计数据（不区分行业，但排除"其他"行业）
            this.announcementModel.aggregate([
                {
                    $match: {
                        ...baseQuery,
                        winningBidder: companyA,
                        industry: { $ne: "其他" }
                    }
                },
                {
                    $group: {
                        _id: {
                            year: { $year: "$publishDate" },
                            month: { $month: "$publishDate" }
                        },
                        count: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                },
                {
                    $sort: { "_id.year": 1, "_id.month": 1 }
                }
            ]).exec(),
            // companyB的总体每月统计数据（不区分行业，但排除"其他"行业）
            this.announcementModel.aggregate([
                {
                    $match: {
                        ...baseQuery,
                        winningBidder: companyB,
                        industry: { $ne: "其他" }
                    }
                },
                {
                    $group: {
                        _id: {
                            year: { $year: "$publishDate" },
                            month: { $month: "$publishDate" }
                        },
                        count: { $sum: 1 },
                        totalAmount: {
                            $sum: {
                                $toDouble: {
                                    $ifNull: ["$winningBidAmount", 0]
                                }
                            }
                        }
                    }
                },
                {
                    $sort: { "_id.year": 1, "_id.month": 1 }
                }
            ]).exec()
        ]);

        // 处理总体每月统计数据
        const formatMonthlyData = (monthlyResult: any[]) => {
            return monthlyResult.map(item => ({
                year: item._id.year,
                month: item._id.month,
                monthKey: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`, // 格式化为 YYYY-MM
                count: item.count,
                totalAmount: item.totalAmount
            }));
        };

        const companyAMonthlyData = formatMonthlyData(companyAMonthlyResult);
        const companyBMonthlyData = formatMonthlyData(companyBMonthlyResult);

        return {
            companyA: {
                name: companyA,
                count: companyATotalData.totalCount,
                totalAmount: companyATotalData.totalAmount,
                percentage: grandTotal > 0 ? Number(((companyATotalData.totalCount / grandTotal) * 100).toFixed(2)) : 0,
                monthlyData: companyAMonthlyData, // 总体每月统计数据（不区分行业）
                industryBreakdown: industryResults.map(item => ({
                    industry: item.industry,
                    count: item.companyA.count,
                    totalAmount: item.companyA.totalAmount,
                    percentage: item.companyA.percentage // 该行业在companyA所有项目中的占比
                }))
            },
            companyB: {
                name: companyB,
                count: companyBTotalData.totalCount,
                totalAmount: companyBTotalData.totalAmount,
                percentage: grandTotal > 0 ? Number(((companyBTotalData.totalCount / grandTotal) * 100).toFixed(2)) : 0,
                monthlyData: companyBMonthlyData, // 总体每月统计数据（不区分行业）
                industryBreakdown: industryResults.map(item => ({
                    industry: item.industry,
                    count: item.companyB.count,
                    totalAmount: item.companyB.totalAmount,
                    percentage: item.companyB.percentage // 该行业在companyB所有项目中的占比
                }))
            },
            summary: {
                totalCount: grandTotal,
                companyAAndBCount: companyATotalData.totalCount + companyBTotalData.totalCount,
                companyAAndBTotalAmount: companyATotalData.totalAmount + companyBTotalData.totalAmount,
                companyAAndBPercentage: grandTotal > 0 ? Number((((companyATotalData.totalCount + companyBTotalData.totalCount) / grandTotal) * 100).toFixed(2)) : 0
            },
            marketShare: marketShareData, // 按行业的市场占有率数据
            provinceDistribution: provinceDistributionData // 按行业和省份的分布数据
        };
    }

    // 获取按行业的市场占有率数据（不区分省份）
    private async getMarketShareByIndustry(companyA: string, companyB: string, baseQuery: any, allIndustries: string[]): Promise<any> {
        const marketShareResults = [];

        for (const industry of allIndustries) {
            const industryQuery = {
                ...baseQuery,
                industry: { $regex: new RegExp(`(^|,)\\s*${industry}\\s*(,|$)`), $options: 'i' }
            };

            const [companyAIndustryResult, companyBIndustryResult, totalIndustryResult] = await Promise.all([
                // companyA在该行业的统计
                this.announcementModel.aggregate([
                    { $match: { ...industryQuery, winningBidder: companyA } },
                    {
                        $group: {
                            _id: null,
                            count: { $sum: 1 },
                            totalAmount: {
                                $sum: {
                                    $toDouble: {
                                        $ifNull: ["$winningBidAmount", 0]
                                    }
                                }
                            }
                        }
                    }
                ]).exec(),
                // companyB在该行业的统计
                this.announcementModel.aggregate([
                    { $match: { ...industryQuery, winningBidder: companyB } },
                    {
                        $group: {
                            _id: null,
                            count: { $sum: 1 },
                            totalAmount: {
                                $sum: {
                                    $toDouble: {
                                        $ifNull: ["$winningBidAmount", 0]
                                    }
                                }
                            }
                        }
                    }
                ]).exec(),
                // 该行业的总统计
                this.announcementModel.aggregate([
                    { $match: industryQuery },
                    {
                        $group: {
                            _id: null,
                            totalCount: { $sum: 1 }
                        }
                    }
                ]).exec()
            ]);

            const companyAIndustryData = companyAIndustryResult.length > 0 ? companyAIndustryResult[0] : { count: 0, totalAmount: 0 };
            const companyBIndustryData = companyBIndustryResult.length > 0 ? companyBIndustryResult[0] : { count: 0, totalAmount: 0 };
            const totalIndustryCount = totalIndustryResult.length > 0 ? totalIndustryResult[0].totalCount : 0;

            // 计算市场占有率
            const companyAMarketShare = totalIndustryCount > 0 ? Number(((companyAIndustryData.count / totalIndustryCount) * 100).toFixed(2)) : 0;
            const companyBMarketShare = totalIndustryCount > 0 ? Number(((companyBIndustryData.count / totalIndustryCount) * 100).toFixed(2)) : 0;

            marketShareResults.push({
                industry: industry,
                totalCount: totalIndustryCount,
                companyA: {
                    count: companyAIndustryData.count,
                    totalAmount: companyAIndustryData.totalAmount,
                    marketShare: companyAMarketShare
                },
                companyB: {
                    count: companyBIndustryData.count,
                    totalAmount: companyBIndustryData.totalAmount,
                    marketShare: companyBMarketShare
                }
            });
        }

        return marketShareResults;
    }

    // 获取按行业和省份分组的分布数据
    private async getProvinceDistributionByIndustry(companyA: string, companyB: string, baseQuery: any, allIndustries: string[]): Promise<any> {
        const distributionResults = [];

        for (const industry of allIndustries) {
            const industryQuery = {
                ...baseQuery,
                industry: { $regex: new RegExp(`(^|,)\\s*${industry}\\s*(,|$)`), $options: 'i' }
            };

            const [companyAProvinceData, companyBProvinceData] = await Promise.all([
                // companyA在该行业各省份的统计
                this.announcementModel.aggregate([
                    { $match: { ...industryQuery, winningBidder: companyA } },
                    {
                        $group: {
                            _id: "$provinceName",
                            count: { $sum: 1 },
                            totalAmount: {
                                $sum: {
                                    $toDouble: {
                                        $ifNull: ["$winningBidAmount", 0]
                                    }
                                }
                            }
                        }
                    },
                    { $sort: { "_id": 1 } }
                ]).exec(),
                // companyB在该行业各省份的统计
                this.announcementModel.aggregate([
                    { $match: { ...industryQuery, winningBidder: companyB } },
                    {
                        $group: {
                            _id: "$provinceName",
                            count: { $sum: 1 },
                            totalAmount: {
                                $sum: {
                                    $toDouble: {
                                        $ifNull: ["$winningBidAmount", 0]
                                    }
                                }
                            }
                        }
                    },
                    { $sort: { "_id": 1 } }
                ]).exec()
            ]);

            // 合并省份数据
            const provinceMap = new Map();

            // 处理companyA数据
            companyAProvinceData.forEach(item => {
                if (item._id) { // 排除provinceName为null的数据
                    provinceMap.set(item._id, {
                        province: item._id,
                        companyA: { count: item.count, totalAmount: item.totalAmount },
                        companyB: { count: 0, totalAmount: 0 }
                    });
                }
            });

            // 处理companyB数据
            companyBProvinceData.forEach(item => {
                if (item._id) {
                    if (provinceMap.has(item._id)) {
                        provinceMap.get(item._id).companyB = { count: item.count, totalAmount: item.totalAmount };
                    } else {
                        provinceMap.set(item._id, {
                            province: item._id,
                            companyA: { count: 0, totalAmount: 0 },
                            companyB: { count: item.count, totalAmount: item.totalAmount }
                        });
                    }
                }
            });

            // 转换为数组并排序
            const provinceResults = Array.from(provinceMap.values()).sort((a, b) => a.province.localeCompare(b.province));

            distributionResults.push({
                industry: industry,
                provinces: provinceResults
            });
        }

        return distributionResults;
    }
}
