import {
  Controller,
  Get,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AreaService } from './area.service';

@Controller('/api/area')
export class AreaController {
  constructor(private readonly areaService: AreaService) {}

  @Get()
  async getAllAreas() {
    return this.areaService.getAllAreas();
  }

  @Get('/getProvinceCity')
  async getProvinceCity(@Query('areaCode') areaCode: string) {
    try {
      if (!areaCode) {
        throw new HttpException(
          'areaCode 参数不能为空',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.areaService.getProvinceCity(areaCode);

      if (result === null) {
        return {
          success: false,
          data: null,
          message: '未找到对应的区域信息',
        };
      }

      return {
        success: true,
        data: result,
        message: '获取省市区信息成功',
      };
    } catch (error) {
      throw new HttpException(
        error.message || '获取省市区信息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
